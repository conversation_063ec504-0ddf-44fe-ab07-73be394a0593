#!/usr/bin/env python3
"""
AI Astrologer System Prototype
Simple integration of existing components for RAG-enhanced astrology consultations.
"""

import asyncio
import json
import os
from typing import List, Dict, Optional
import logging

# Import existing components
from gpt4o_client import initialize_gpt4o_client
from pinecone_client import PineconeClient
from config import config

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class AstrologerPrototype:
    """
    Simple AI Astrologer prototype integrating existing GPT-4 and Pinecone components.
    """
    
    def __init__(self, similarity_threshold: float = 0.75):
        """
        Initialize the astrologer prototype.

        Args:
            similarity_threshold (float): Minimum similarity score for RAG results
        """
        self.similarity_threshold = similarity_threshold
        self.astrologer_config = {}
        self._base_persona_text = None  # Cache the persona text

        # Initialize existing clients
        logger.info("🚀 Initializing AI Astrologer Prototype...")
        self.gpt4o_client = initialize_gpt4o_client()
        self.pinecone_client = PineconeClient()

        # Load astrologer configuration and cache persona
        self._load_astrologer_config()
        self._base_persona_text = self._get_base_persona_text()
        
    def _load_astrologer_config(self) -> bool:
        """Load astrologer configuration from JSON file."""
        config_path = "prompts/astrologer2.json"
        
        try:
            if not os.path.exists(config_path):
                logger.error(f"❌ Configuration file not found: {config_path}")
                return False
                
            with open(config_path, 'r', encoding='utf-8') as file:
                self.astrologer_config = json.load(file)
                
            logger.info(f"✅ Loaded astrologer configuration from {config_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error loading configuration: {e}")
            return False
            
    def _get_base_persona_text(self) -> str:
        """Get the base astrologer persona as a simple string representation."""
        if not self.astrologer_config:
            return "You are a professional astrologer. Provide helpful and insightful astrological guidance."

        # Simply convert the entire JSON config to a readable string format
        return json.dumps(self.astrologer_config, indent=2)

    def _create_system_prompt(self, rag_context: str = "") -> str:
        """
        Create system prompt with astrologer persona and optional RAG context.

        Args:
            rag_context (str): RAG context to include in prompt

        Returns:
            str: Complete system prompt
        """
        # Use cached base persona for efficiency
        base_prompt = self._base_persona_text

        # Add RAG context if available
        if rag_context:
            return f"{base_prompt}\n\nRelevant Astrological Knowledge:\n{rag_context}"
        else:
            return base_prompt
        
    def _filter_rag_results(self, matches: List[Dict]) -> List[Dict]:
        """
        Filter RAG results based on similarity threshold.
        
        Args:
            matches (List[Dict]): Raw matches from Pinecone
            
        Returns:
            List[Dict]: Filtered high-quality matches
        """
        if not matches:
            return []
            
        # Filter by similarity threshold
        filtered_matches = []
        for match in matches:
            score = match.get('score', 0.0)
            if score >= self.similarity_threshold:
                filtered_matches.append(match)
                
        logger.info(f"📊 RAG Filtering: {len(matches)} total → {len(filtered_matches)} above threshold {self.similarity_threshold}")
        return filtered_matches
        
    def _format_rag_context(self, matches: List[Dict]) -> str:
        """
        Format filtered RAG matches into context text.

        Args:
            matches (List[Dict]): Filtered matches from Pinecone

        Returns:
            str: Formatted context text
        """
        if not matches:
            return ""

        context_parts = []
        for i, match in enumerate(matches, 1):
            metadata = match.get('metadata', {})

            # Extract content from metadata - try different possible keys
            content = (metadata.get('text', '') or
                      metadata.get('content', '') or
                      metadata.get('chunk_text', '') or
                      metadata.get('paragraph_text', ''))

            source = metadata.get('source', metadata.get('file_name', f'Reference {i}'))
            score = match.get('score', 0.0)

            if content:
                # Truncate very long content to keep prompt manageable
                if len(content) > 500:
                    content = content[:500] + "..."
                context_parts.append(f"Reference {i} (Relevance: {score:.3f}):\nSource: {source}\nContent: {content}")

        return "\n\n".join(context_parts)
        
    async def get_astrology_response(self, user_query: str, enable_rag: bool = True) -> Dict:
        """
        Get astrology response with optional RAG enhancement.
        
        Args:
            user_query (str): User's astrology question
            enable_rag (bool): Whether to use RAG enhancement
            
        Returns:
            Dict: Response data including text, RAG info, and metadata
        """
        logger.info(f"🔮 Processing astrology query: {user_query[:100]}...")
        
        rag_context = ""
        rag_info = {"enabled": enable_rag, "matches_found": 0, "matches_used": 0}
        
        # Perform RAG retrieval if enabled
        if enable_rag:
            try:
                # Query Pinecone with specific settings
                raw_matches = self.pinecone_client.query_vectors(
                    query=user_query,
                    index_name="astrology-text",
                    namespace="hierarchical_chunking_strategy",
                    top_k=5
                )
                
                rag_info["matches_found"] = len(raw_matches)
                
                # Filter results by similarity threshold
                filtered_matches = self._filter_rag_results(raw_matches)
                rag_info["matches_used"] = len(filtered_matches)
                
                if filtered_matches:
                    rag_context = self._format_rag_context(filtered_matches)
                    logger.info(f"✅ RAG: Using {len(filtered_matches)} high-quality matches")
                    # Debug: log first match metadata to understand structure
                    if filtered_matches:
                        first_match = filtered_matches[0]
                        logger.info(f"📋 Sample metadata keys: {list(first_match.get('metadata', {}).keys())}")
                else:
                    logger.warning(f"⚠️ RAG: No matches above threshold {self.similarity_threshold}, using fallback")
                    
            except Exception as e:
                logger.error(f"❌ RAG retrieval failed: {e}")
                rag_info["error"] = str(e)
                
        # Create system prompt with persona and context
        system_prompt = self._create_system_prompt(rag_context)
        
        # Generate response using GPT-4
        try:
            response_text = await self.gpt4o_client.generate_response(
                system_prompt=system_prompt,
                user_input=user_query,
                temperature=0.7,
                max_tokens=2048
            )
            
            logger.info("✅ Generated astrology response successfully")
            
            return {
                "response": response_text,
                "rag_info": rag_info,
                "system_prompt_length": len(system_prompt),
                "has_rag_context": bool(rag_context),
                "rag_context": rag_context,
                "similarity_threshold": self.similarity_threshold,
                "prompt_data": {
                    "base_persona": self._base_persona_text,
                    "rag_context": rag_context,
                    "user_question": user_query,
                    "final_system_prompt": system_prompt,
                    "word_count": len(system_prompt.split()),
                    "estimated_tokens": len(system_prompt) // 4
                }
            }
            
        except Exception as e:
            logger.error(f"❌ Response generation failed: {e}")
            return {
                "response": f"I apologize, but I encountered an error while generating your astrology reading: {e}",
                "rag_info": rag_info,
                "error": str(e)
            }


async def test_astrologer_prototype():
    """Test the astrologer prototype with sample questions."""
    print("🌟 AI Astrologer Prototype Test")
    print("=" * 50)
    
    # Initialize astrologer
    astrologer = AstrologerPrototype(similarity_threshold=0.75)
    
    # Test questions
    test_questions = [
        "What does it mean to have Mars in the 7th house?",
        "How do Saturn transits affect personal growth?",
        "Can you explain the significance of a Grand Trine in a birth chart?"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n📋 Test {i}: {question}")
        print("-" * 40)
        
        # Test with RAG enabled
        result = await astrologer.get_astrology_response(question, enable_rag=True)
        
        print(f"🔮 Response: {result['response'][:200]}...")
        print(f"📊 RAG Info: {result['rag_info']}")
        print(f"🎯 Threshold: {result['similarity_threshold']}")
        
        # Small delay between tests
        await asyncio.sleep(1)


if __name__ == "__main__":
    # Run the test
    asyncio.run(test_astrologer_prototype())
