#!/usr/bin/env python3
"""
測試各個模組的導入，找出 proxies 錯誤的來源
"""

print("🔍 測試模組導入...")

try:
    print("1. 測試 config 導入...")
    from config import config
    print("✅ config 導入成功")
except Exception as e:
    print(f"❌ config 導入失敗: {e}")
    exit(1)

try:
    print("2. 測試 OpenAI 導入...")
    from fixed_openai_clients import AzureOpenAI, AsyncAzureOpenAI
    print("✅ 修復後的 OpenAI 導入成功")
except Exception as e:
    print(f"❌ 修復後的 OpenAI 導入失敗: {e}")
    exit(1)

try:
    print("3. 測試 Pinecone 導入...")
    from pinecone import Pinecone
    print("✅ Pinecone 導入成功")
except Exception as e:
    print(f"❌ Pinecone 導入失敗: {e}")
    exit(1)

try:
    print("4. 測試 AzureOpenAI 客戶端初始化...")
    client = AzureOpenAI(
        api_key=config.EMBED_KEY,
        api_version="2023-05-15",
        azure_endpoint="https://fluxmind.openai.azure.com/",
    )
    print("✅ AzureOpenAI 客戶端初始化成功")
except Exception as e:
    print(f"❌ AzureOpenAI 客戶端初始化失敗: {e}")

try:
    print("5. 測試 AsyncAzureOpenAI 客戶端初始化...")
    async_client = AsyncAzureOpenAI(
        api_key=config.EMBED_KEY,
        api_version="2023-05-15",
        azure_endpoint="https://fluxmind.openai.azure.com/",
    )
    print("✅ AsyncAzureOpenAI 客戶端初始化成功")
except Exception as e:
    print(f"❌ AsyncAzureOpenAI 客戶端初始化失敗: {e}")

try:
    print("6. 測試 Pinecone 客戶端初始化...")
    if config.PINECONE_API_KEY and config.PINECONE_API_KEY != "test_key_placeholder":
        pc = Pinecone(api_key=config.PINECONE_API_KEY)
        print("✅ Pinecone 客戶端初始化成功")
    else:
        print("⚠️  Pinecone API key 未配置，跳過測試")
except Exception as e:
    print(f"❌ Pinecone 客戶端初始化失敗: {e}")

try:
    print("7. 測試 GPT4oClient 導入...")
    from gpt4o_client import GPT4oClient
    print("✅ GPT4oClient 導入成功")
except Exception as e:
    print(f"❌ GPT4oClient 導入失敗: {e}")

try:
    print("8. 測試 PineconeClient 導入...")
    from pinecone_client import PineconeClient
    print("✅ PineconeClient 導入成功")
except Exception as e:
    print(f"❌ PineconeClient 導入失敗: {e}")

try:
    print("9. 測試 PineconeClient 初始化...")
    pc_client = PineconeClient()
    print("✅ PineconeClient 初始化成功")
except Exception as e:
    print(f"❌ PineconeClient 初始化失敗: {e}")

try:
    print("10. 測試 GPT4oClient 初始化...")
    gpt_client = GPT4oClient()
    print("✅ GPT4oClient 初始化成功")
except Exception as e:
    print(f"❌ GPT4oClient 初始化失敗: {e}")

print("\n🎉 所有測試完成！")
