# F-string 占星師 Prompt 模板使用指南

## 概述

我們提供了兩個版本的 F-string 占星師 prompt 模板：

1. **完整版** (`astrologer_fstring_template.py`) - 包含所有可配置參數
2. **簡化版** (`simple_astrologer_template.py`) - 專注於核心參數

## 簡化版函數 (推薦)

### 函數簽名

```python
def create_astrologer_prompt(
    style: str,                           # 占星師風格
    specialization: str,                  # 專精領域  
    reading_steps: Union[str, List[str]], # 星盤解讀步驟
    interpretation_style: str,            # 解釋風格
    word_count: int                       # 目標字數
) -> str:
```

### 參數說明

| 參數 | 類型 | 說明 | 範例 |
|------|------|------|------|
| `style` | str | 占星師風格 | "溫和療癒型", "理性分析型", "神秘智慧型", "直接犀利型" |
| `specialization` | str | 專精領域 | "本命盤解讀", "流年預測", "合盤分析", "卜卦占星" |
| `reading_steps` | str/List[str] | 解讀步驟 | 字符串或步驟列表 |
| `interpretation_style` | str | 解釋風格 | "詳細分析", "簡潔明瞭", "富有洞察力" |
| `word_count` | int | 目標字數 | 600, 800, 1000 |

## 使用範例

### 範例 1: 溫和療癒型占星師

```python
from simple_astrologer_template import create_astrologer_prompt

prompt = create_astrologer_prompt(
    style="溫和療癒型",
    specialization="本命盤解讀", 
    reading_steps="先看整體格局，再分析太陽月亮上升，最後看行星相位",
    interpretation_style="詳細分析",
    word_count=600
)
```

### 範例 2: 理性分析型占星師

```python
steps_list = [
    "觀察星盤整體能量分佈",
    "分析太陽、月亮、上升星座",
    "解讀各行星在星座和宮位的表現", 
    "分析重要相位關係",
    "整合資訊並連結生活實際"
]

prompt = create_astrologer_prompt(
    style="理性分析型",
    specialization="流年預測和合盤分析",
    reading_steps=steps_list,
    interpretation_style="簡潔明瞭且富有洞察力",
    word_count=800
)
```

### 範例 3: 神秘智慧型占星師

```python
prompt = create_astrologer_prompt(
    style="神秘智慧型",
    specialization="靈魂成長和生命課題探索",
    reading_steps="從靈魂層面解讀星盤訊息，連結宇宙智慧與個人成長路徑",
    interpretation_style="富有詩意且具靈性深度",
    word_count=700
)
```

### 範例 4: 直接犀利型占星師

```python
prompt = create_astrologer_prompt(
    style="直接犀利型",
    specialization="事業發展和人際關係分析",
    reading_steps=[
        "快速掃描關鍵配置",
        "直接指出核心議題", 
        "提供實用建議"
    ],
    interpretation_style="直接且實用",
    word_count=500
)
```

## 完整版函數 (進階使用)

如果需要更多自定義選項，可以使用完整版函數：

```python
from astrologer_fstring_template import create_astrologer_prompt, AstrologerStyle

prompt = create_astrologer_prompt(
    style=AstrologerStyle.HEALING,
    astrology_school="現代心理占星",
    specialization=["本命盤解讀", "流年預測"],
    emotional_expression="溫暖共情",
    interpretation_style="溫暖而深入的分析",
    word_count=600,
    life_stage_focus="青年期",
    include_ethics=True,
    include_boundaries=True
)
```

## 風格特色對比

| 風格 | 特色 | 適用場景 |
|------|------|----------|
| **溫和療癒型** | 語調溫暖、包容，善於撫慰情緒 | 情感支持、心理療癒 |
| **理性分析型** | 邏輯清晰，重視客觀分析 | 學術研究、客觀評估 |
| **神秘智慧型** | 用詞富有詩意，帶有靈性色彩 | 靈性成長、深度探索 |
| **直接犀利型** | 表達精準、不迴避困難議題 | 實用建議、快速診斷 |

## 專精領域建議

### 常見專精領域
- **本命盤解讀** - 基礎人格分析
- **流年預測** - 時間趨勢分析
- **合盤分析** - 關係相容性
- **卜卦占星** - 具體問題解答
- **擇時占星** - 最佳時機選擇
- **事業占星** - 職涯發展指導
- **醫療占星** - 健康趨勢分析

### 組合建議
- "本命盤解讀和流年預測"
- "合盤分析和關係諮詢"
- "事業發展和財運分析"
- "靈魂成長和生命課題探索"

## 解讀步驟範例

### 標準六步驟
```python
standard_steps = [
    "觀察星盤整體能量分佈",
    "分析太陽、月亮、上升星座",
    "解讀各行星在星座和宮位的表現",
    "分析重要相位關係",
    "識別特殊格局和配置",
    "整合資訊並連結生活實際"
]
```

### 簡化三步驟
```python
simple_steps = [
    "分析核心三要素（太陽、月亮、上升）",
    "解讀關鍵行星配置",
    "提供實用生活建議"
]
```

### 深度分析步驟
```python
deep_steps = [
    "整體星盤格局和能量分析",
    "個人行星群（太陽到火星）詳細解讀",
    "社會行星（木星、土星）的影響",
    "外行星（天王星、海王星、冥王星）的世代特質",
    "宮位系統和生活領域分析",
    "相位網絡和動態關係",
    "特殊點位（南北交點、小行星等）",
    "綜合整合和生命主題提煉"
]
```

## 解釋風格選項

- **詳細分析** - 深入且全面的解讀
- **簡潔明瞭** - 重點突出，易於理解
- **富有洞察力** - 深度挖掘，提供獨特視角
- **實用導向** - 重視實際應用和建議
- **富有詩意** - 優美的表達方式
- **科學客觀** - 理性和邏輯性強
- **溫暖鼓勵** - 正向且支持性的語調

## 字數建議

- **簡短回應**: 300-500字 - 適合快速諮詢
- **標準回應**: 600-800字 - 平衡深度和可讀性
- **詳細分析**: 1000-1500字 - 深度解讀
- **完整報告**: 2000字以上 - 全面分析

## 最佳實踐

1. **風格一致性**: 確保所有參數相互配合
2. **目標明確**: 根據使用場景選擇合適的配置
3. **測試調整**: 生成後測試效果，必要時調整參數
4. **保存配置**: 將成功的配置保存為模板

## 故障排除

### 常見問題
1. **風格不匹配**: 檢查 style 和 interpretation_style 是否協調
2. **步驟過於複雜**: 簡化 reading_steps 或使用字符串格式
3. **字數控制**: 調整 word_count 參數以符合需求

### 調試技巧
```python
# 檢查生成的 prompt 長度
prompt = create_astrologer_prompt(...)
print(f"Prompt 長度: {len(prompt)} 字符")

# 預覽 prompt 開頭
print(prompt[:200] + "...")
```

這個 F-string 模板系統提供了靈活且易用的方式來創建各種風格的占星師 AI prompt，滿足不同場景和需求。
