"""
Test script to compare simple and hierarchical chunking strategies
"""

import os
from simple_chunk_strategy import process_pdf_simple
from hierarchical_chunking_strategy import process_pdf_hierarchical


def compare_strategies(pdf_path: str):
    """
    Compare simple and hierarchical chunking strategies on the same PDF
    
    Args:
        pdf_path (str): Path to the PDF file to test
    """
    print("=" * 60)
    print("CHUNKING STRATEGY COMPARISON")
    print("=" * 60)
    print(f"Testing file: {os.path.basename(pdf_path)}")
    print()
    
    # Test simple chunking strategy
    print("1. SIMPLE CHUNKING STRATEGY")
    print("-" * 30)
    try:
        simple_chunks = process_pdf_simple(pdf_path, chunk_size=250, chunk_overlap=50)
        print(f"✅ Simple strategy generated {len(simple_chunks)} chunks")
        
        if simple_chunks:
            example = simple_chunks[0]
            print(f"\nExample chunk:")
            print(f"  Text length: {len(example['chunk_text'])} characters")
            print(f"  Chunk index: {example['chunk_index']}")
            print(f"  Source file: {example['source_file']}")
            print(f"  Text preview: {example['chunk_text'][:150]}...")
            
    except Exception as e:
        print(f"❌ Simple strategy failed: {str(e)}")
        simple_chunks = []
    
    print("\n" + "=" * 60)
    
    # Test hierarchical chunking strategy
    print("2. HIERARCHICAL CHUNKING STRATEGY")
    print("-" * 35)
    try:
        hierarchical_chunks = process_pdf_hierarchical(pdf_path)
        print(f"✅ Hierarchical strategy generated {len(hierarchical_chunks)} chunks")
        
        if hierarchical_chunks:
            example = hierarchical_chunks[0]
            print(f"\nExample chunk:")
            print(f"  Sentence text length: {len(example['chunk_text'])} characters")
            print(f"  Chunk index: {example['chunk_index']}")
            print(f"  Paragraph index: {example['paragraph_index']}")
            print(f"  Source file: {example['source_file']}")
            print(f"  Sentence text: {example['chunk_text']}")
            print(f"  Paragraph context length: {len(example['paragraph_context'])} characters")
            print(f"  Paragraph context preview: {example['paragraph_context'][:150]}...")
            
            # Calculate statistics
            unique_paragraphs = set(chunk['paragraph_index'] for chunk in hierarchical_chunks)
            avg_sentences_per_para = len(hierarchical_chunks) / len(unique_paragraphs)
            
            print(f"\nHierarchical statistics:")
            print(f"  - Total sentence chunks: {len(hierarchical_chunks)}")
            print(f"  - Unique paragraphs: {len(unique_paragraphs)}")
            print(f"  - Average sentences per paragraph: {avg_sentences_per_para:.1f}")
            
    except Exception as e:
        print(f"❌ Hierarchical strategy failed: {str(e)}")
        hierarchical_chunks = []
    
    print("\n" + "=" * 60)
    
    # Compare results
    print("3. COMPARISON SUMMARY")
    print("-" * 20)
    if simple_chunks and hierarchical_chunks:
        ratio = len(hierarchical_chunks) / len(simple_chunks)
        print(f"Simple chunks: {len(simple_chunks)}")
        print(f"Hierarchical chunks: {len(hierarchical_chunks)}")
        print(f"Ratio (hierarchical/simple): {ratio:.2f}x")
        
        # Average chunk sizes
        avg_simple_size = sum(len(chunk['chunk_text']) for chunk in simple_chunks) / len(simple_chunks)
        avg_hierarchical_size = sum(len(chunk['chunk_text']) for chunk in hierarchical_chunks) / len(hierarchical_chunks)
        
        print(f"Average simple chunk size: {avg_simple_size:.1f} characters")
        print(f"Average hierarchical chunk size: {avg_hierarchical_size:.1f} characters")
        
        print(f"\nKey differences:")
        print(f"- Simple strategy: Larger chunks, fewer total pieces")
        print(f"- Hierarchical strategy: Sentence-level chunks with paragraph context")
        print(f"- Hierarchical provides better context preservation for RAG")
        
    else:
        print("❌ Cannot compare - one or both strategies failed")
    
    print("=" * 60)


if __name__ == "__main__":
    # Test with the available PDF file
    test_file = "./data/The Twelve Houses_ Understanding the Importance of the 12 -- by Howard Sasportas; illustrated by Jacqueline Clare.pdf"
    
    if os.path.exists(test_file):
        compare_strategies(test_file)
    else:
        print(f"Test file not found: {test_file}")
        print("Available files in ./data/:")
        if os.path.exists("./data"):
            for file in os.listdir("./data"):
                if file.endswith('.pdf'):
                    print(f"  - {file}")
        else:
            print("  No data directory found")
