# PDF Chunking Strategies Implementation

This document describes the two PDF chunking strategy implementations created based on the existing `chunk_processor.py` file.

## Overview

Two simplified chunking strategy files have been created:

1. **`simple_chunk_strategy.py`** - Basic text chunking using RecursiveCharacterTextSplitter
2. **`hierarchical_chunking_strategy.py`** - Hierarchical chunking optimized for RAG retrieval quality

## File 1: Simple Chunk Strategy (`simple_chunk_strategy.py`)

### Purpose
Extract and simplify the PDFChunker functionality for basic text chunking using only `RecursiveCharacterTextSplitter` from langchain_text_splitters.

### Core Functions

#### `load_pdf(file_path: str) -> str`
- **Purpose**: Load and extract text content from PDF documents
- **Parameters**: `file_path` - Path to the PDF file
- **Returns**: Extracted text content as string
- **Features**: 
  - Uses PyPDF2 for PDF reading
  - Handles page-by-page extraction
  - Progress reporting every 10 pages
  - Error handling for individual page failures

#### `extract_paragraphs(text: str) -> List[str]`
- **Purpose**: Extract paragraphs from the loaded text content
- **Parameters**: `text` - Input text content
- **Returns**: List of paragraph strings
- **Features**:
  - Splits text by double newlines (`\n\n`)
  - Filters out very short paragraphs (< 20 characters)
  - Removes digit-only content

#### `create_metadata(chunks: List[str], source_file: str) -> List[Dict[str, Any]]`
- **Purpose**: Generate metadata for each chunk with specified structure
- **Parameters**: 
  - `chunks` - List of text chunks
  - `source_file` - Name of the source file
- **Returns**: List of metadata dictionaries
- **Metadata Structure**:
  - `chunk_text` (str): The actual text content of the chunk
  - `chunk_index` (int): Sequential index number starting from 0
  - `source_file` (str): Name of the source file (not full path)

### Usage Example
```python
from simple_chunk_strategy import process_pdf_simple

chunks = process_pdf_simple("document.pdf", chunk_size=250, chunk_overlap=50)
for chunk in chunks:
    print(f"Chunk {chunk['chunk_index']}: {chunk['chunk_text'][:100]}...")
```

## File 2: Hierarchical Chunking Strategy (`hierarchical_chunking_strategy.py`)

### Purpose
Implement a hierarchical chunking approach optimized for RAG retrieval quality. The strategy extracts complete paragraphs as context units, then splits paragraphs into individual sentences for embedding, while attaching the full paragraph text as metadata to preserve context.

### Core Functions

#### `load_pdf(file_path: str) -> str`
- **Purpose**: Load PDF content (same as simple strategy)
- **Implementation**: Identical to simple strategy for consistency

#### `extract_paragraphs(text: str) -> List[str]`
- **Purpose**: Extract complete paragraphs from text
- **Features**: Enhanced filtering including regex patterns for page markers

#### `split_paragraph_to_sentences(paragraph: str) -> List[str]`
- **Purpose**: Split paragraph into individual sentences
- **Parameters**: `paragraph` - Input paragraph text
- **Returns**: List of individual sentences
- **Features**:
  - Uses RecursiveCharacterTextSplitter with sentence-optimized separators
  - Smaller chunk size (200 characters) for sentence-level splitting
  - Filters sentences shorter than 15 characters

#### `create_hierarchical_chunks(paragraphs: List[str], source_file: str) -> List[Dict[str, Any]]`
- **Purpose**: Create sentence chunks with paragraph context metadata
- **Parameters**:
  - `paragraphs` - List of complete paragraphs
  - `source_file` - Source file name
- **Returns**: List of sentence chunks with hierarchical metadata
- **Metadata Structure**:
  - `chunk_text` (str): Individual sentence text
  - `chunk_index` (int): Sequential index of the sentence chunk
  - `paragraph_context` (str): Full paragraph text containing this sentence
  - `paragraph_index` (int): Index of the source paragraph
  - `source_file` (str): Source file name

### Usage Example
```python
from hierarchical_chunking_strategy import process_pdf_hierarchical

chunks = process_pdf_hierarchical("document.pdf")
for chunk in chunks:
    print(f"Sentence: {chunk['chunk_text']}")
    print(f"Context: {chunk['paragraph_context'][:100]}...")
```

## Comparison Test (`test_chunking_strategies.py`)

A comprehensive test script that compares both strategies on the same PDF file, showing:

- Number of chunks generated by each strategy
- Average chunk sizes
- Example outputs
- Performance statistics

### Test Results (Sample PDF: "The Twelve Houses")
- **Simple Strategy**: 1,294 chunks, average 210 characters per chunk
- **Hierarchical Strategy**: 2,001 chunks, average 136 characters per chunk
- **Ratio**: 1.55x more chunks with hierarchical approach
- **Paragraphs Processed**: 124 paragraphs → 16.1 sentences per paragraph average

## Key Differences

### Simple Strategy
- **Approach**: Traditional text splitting with configurable chunk sizes
- **Output**: Larger, variable-sized chunks
- **Use Case**: General text processing, when context preservation is less critical
- **Advantages**: Fewer chunks, faster processing, simpler metadata

### Hierarchical Strategy
- **Approach**: Paragraph-aware sentence-level chunking with context preservation
- **Output**: Smaller, sentence-based chunks with full paragraph context
- **Use Case**: RAG applications where context quality is crucial
- **Advantages**: Better semantic coherence, preserved context, optimized for embedding

## Dependencies

Both implementations require:
- `PyPDF2` - For PDF text extraction
- `langchain-text-splitters` - For intelligent text splitting
- `typing` - For type hints (built-in)
- `os`, `re` - For file operations and regex (built-in)

## Installation

```bash
pip install PyPDF2 langchain-text-splitters
```

## Error Handling

Both implementations include comprehensive error handling for:
- Missing dependencies
- File not found errors
- PDF reading failures
- Individual page extraction errors
- Empty content scenarios

## Code Quality Features

- **Type Hints**: All functions include proper type annotations
- **Docstrings**: Comprehensive documentation for all functions
- **Error Messages**: Clear, actionable error messages
- **Progress Reporting**: User feedback during long operations
- **Modular Design**: Functions can be used independently
- **Consistent Naming**: Following Python naming conventions (snake_case)
- **Edge Case Handling**: Robust handling of various input scenarios
