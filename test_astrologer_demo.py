#!/usr/bin/env python3
"""
Demo script to test the AI Astrologer prototype with different similarity thresholds.
Shows both successful RAG scenarios and fallback behavior.
"""

import asyncio
from astrologer_prototype import AstrologerPrototype


async def demo_astrologer_system():
    """Demonstrate the astrologer system with different threshold settings."""
    print("🌟 AI Astrologer System Demo")
    print("=" * 60)
    
    # Test question
    question = "What does <PERSON> in the 7th house mean for relationships?"
    
    # Test with different thresholds to show both RAG and fallback scenarios
    thresholds = [0.6, 0.75, 0.9]
    
    for threshold in thresholds:
        print(f"\n🎯 Testing with similarity threshold: {threshold}")
        print("-" * 50)
        
        # Initialize astrologer with specific threshold
        astrologer = AstrologerPrototype(similarity_threshold=threshold)
        
        # Get response
        result = await astrologer.get_astrology_response(question, enable_rag=True)
        
        # Display results
        print(f"📊 RAG Results:")
        print(f"   - Matches found: {result['rag_info']['matches_found']}")
        print(f"   - Matches used: {result['rag_info']['matches_used']}")
        print(f"   - Has RAG context: {result['has_rag_context']}")
        print(f"   - System prompt length: {result['system_prompt_length']} chars")
        
        print(f"\n🔮 Response preview:")
        response_preview = result['response'][:300] + "..." if len(result['response']) > 300 else result['response']
        print(response_preview)
        
        # Small delay between tests
        await asyncio.sleep(2)
    
    print("\n" + "=" * 60)
    print("✅ Demo completed! The system successfully:")
    print("   • Loads astrologer configuration from JSON")
    print("   • Performs RAG retrieval from Pinecone")
    print("   • Applies similarity threshold filtering")
    print("   • Provides fallback behavior when needed")
    print("   • Generates responses with astrologer persona")


async def interactive_demo():
    """Interactive demo where user can ask questions."""
    print("\n🌟 Interactive AI Astrologer Demo")
    print("=" * 50)
    print("Ask any astrology question, or type 'quit' to exit.")
    
    # Initialize astrologer with moderate threshold
    astrologer = AstrologerPrototype(similarity_threshold=0.7)
    
    while True:
        try:
            # Get user input
            user_question = input("\n🤔 Your astrology question: ").strip()
            
            if user_question.lower() in ['quit', 'exit', 'q']:
                print("👋 Thank you for using the AI Astrologer! Goodbye!")
                break
                
            if not user_question:
                continue
                
            print("\n🔮 Consulting the stars...")
            
            # Get astrologer response
            result = await astrologer.get_astrology_response(user_question, enable_rag=True)
            
            # Display response
            print(f"\n📊 RAG Info: {result['rag_info']['matches_found']} matches found, {result['rag_info']['matches_used']} used")
            print(f"🎯 Similarity threshold: {result['similarity_threshold']}")
            print("\n" + "="*50)
            print(result['response'])
            print("="*50)
            
        except KeyboardInterrupt:
            print("\n👋 Demo interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


if __name__ == "__main__":
    print("Choose demo mode:")
    print("1. Automated threshold comparison demo")
    print("2. Interactive Q&A demo")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        asyncio.run(demo_astrologer_system())
    elif choice == "2":
        asyncio.run(interactive_demo())
    else:
        print("Invalid choice. Running automated demo...")
        asyncio.run(demo_astrologer_system())
