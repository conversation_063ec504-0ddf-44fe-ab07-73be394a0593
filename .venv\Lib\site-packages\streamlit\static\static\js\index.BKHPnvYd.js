import{r,E as M,_ as Q,cE as y,N as m,Q as J,l as ft,s as k,z as bt,aP as mt,cu as ht,H as G,C as z,j as c,br as gt,bH as It,bs as yt,b9 as Tt,bt as Ct,D as wt,bo as X}from"./index.CbQtRkVt.js";import{u as St}from"./uniqueId.-ygIU7IL.js";import{u as vt}from"./FormClearHelper.Cdw5Y7_m.js";import{I as Vt}from"./InputInstructions.D3IDU-eY.js";import{s as xt}from"./sprintf.D7DtBTRn.js";import{I as kt}from"./input.D_45B0P-.js";import"./base-input.DMlw5p7n.js";var Y=r.forwardRef(function(t,e){var a={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return r.createElement(M,Q({iconAttrs:a,iconVerticalAlign:"middle",iconViewBox:"0 0 8 8"},t,{ref:e}),r.createElement("path",{d:"M0 3v2h8V3H0z"}))});Y.displayName="Minus";var Z=r.forwardRef(function(t,e){var a={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return r.createElement(M,Q({iconAttrs:a,iconVerticalAlign:"middle",iconViewBox:"0 0 8 8"},t,{ref:e}),r.createElement("path",{d:"M3 0v3H0v2h3v3h2V5h3V3H5V0H3z"}))});Z.displayName="Plus";const Dt=ft.getLogger("NumberInput");function Et(t){return m(t)||t===""?void 0:t}const F=({value:t,format:e,step:a,dataType:b})=>{if(m(t))return null;let o=Et(e);if(m(o)&&J(a)){const p=a.toString();b===y.DataType.FLOAT&&a!==0&&p.includes(".")&&(o=`%0.${p.split(".")[1].length}f`)}if(m(o))return t.toString();try{return xt.sprintf(o,t)}catch(p){return Dt.warn(`Error in sprintf(${o}, ${t}): ${p}`),String(t)}},Nt=(t,e,a)=>m(t)?!1:t-e>=a,Rt=(t,e,a)=>m(t)?!1:t+e<=a,zt=t=>(t.element.dataType===y.DataType.INT?t.widgetMgr.getIntValue(t.element):t.widgetMgr.getDoubleValue(t.element))??t.element.default??null,K=({step:t,dataType:e})=>t||(e===y.DataType.INT?1:.01),Ft=k("div",{target:"eaba2yi0"})(({theme:t})=>({display:"flex",flexDirection:"row",flexWrap:"nowrap",alignItems:"center",height:t.sizes.minElementHeight,borderWidth:t.sizes.borderWidth,borderStyle:"solid",borderColor:t.colors.widgetBorderColor??t.colors.secondaryBg,transitionDuration:"200ms",transitionProperty:"border",transitionTimingFunction:"cubic-bezier(0.2, 0.8, 0.4, 1)",borderRadius:t.radii.default,overflow:"hidden","&.focused":{borderColor:t.colors.primary},input:{MozAppearance:"textfield","&::-webkit-inner-spin-button, &::-webkit-outer-spin-button":{WebkitAppearance:"none",margin:t.spacing.none}}})),Wt=k("div",{target:"eaba2yi1"})({display:"flex",flexDirection:"row",alignSelf:"stretch"}),q=k("button",{target:"eaba2yi2"})(({theme:t})=>({margin:t.spacing.none,border:"none",height:t.sizes.full,display:"flex",alignItems:"center",width:t.sizes.numberInputControlsWidth,justifyContent:"center",color:t.colors.bodyText,transition:"color 300ms, backgroundColor 300ms",backgroundColor:t.colors.secondaryBg,"&:hover:enabled, &:focus:enabled":{color:t.colors.white,backgroundColor:t.colors.primary,transition:"none",outline:"none"},"&:active":{outline:"none",border:"none"},"&:disabled":{cursor:"not-allowed",color:t.colors.fadedText40}})),Lt=k("div",{target:"eaba2yi3"})(({theme:t,clearable:e})=>({position:"absolute",marginRight:t.spacing.twoXS,left:0,right:`calc(${t.sizes.numberInputControlsWidth} * 2 + ${e?"1em":"0em"})`})),Bt=({disabled:t,element:e,widgetMgr:a,fragmentId:b})=>{const o=bt(),{dataType:p,id:w,formId:f,default:W,format:L,icon:D,min:h,max:g}=e,[B,tt]=mt(),[s,et]=r.useState(()=>K(e)),H=zt({element:e,widgetMgr:a}),[I,T]=r.useState(!1),[l,C]=r.useState(H),[P,S]=r.useState(()=>F({value:H,...e,step:s})),[U,A]=r.useState(!1),v=r.useRef(null),$=r.useRef(St("number_input_")),V=Nt(l,s,h),x=Rt(l,s,g),O=ht({formId:f}),ot=O?a.allowFormEnterToSubmit(f):I,rt=U&&B>o.breakpoints.hideWidgetDetails;r.useEffect(()=>{et(K({step:e.step,dataType:e.dataType}))},[e.dataType,e.step]);const u=r.useCallback(({value:n,source:i})=>{if(J(n)&&(h>n||n>g))v.current?.reportValidity();else{const d=n??W??null;switch(p){case y.DataType.INT:a.setIntValue({id:w,formId:f},d,i,b);break;case y.DataType.FLOAT:a.setDoubleValue({id:w,formId:f},d,i,b);break;default:throw new Error("Invalid data type")}T(!1),C(d),S(F({value:d,dataType:p,format:L,step:s}))}},[h,g,v,a,b,s,p,w,f,W,L]),nt=r.useCallback(()=>{I&&u({value:l,source:{fromUi:!0}}),A(!1)},[I,l,u]),at=r.useCallback(()=>{A(!0)},[]),j=r.useCallback(()=>{const{value:n}=e;e.setValue=!1,C(n??null),S(F({value:n??null,...e,step:s})),u({value:n??null,source:{fromUi:!1}})},[e,s,u]);r.useEffect(()=>{e.setValue?j():u({value:l,source:{fromUi:!1}});const n=v.current;if(n){const i=d=>{d.preventDefault()};return n.addEventListener("wheel",i),()=>{n.removeEventListener("wheel",i)}}},[]),e.setValue&&j();const E=m(e.default)&&!t,st=r.useCallback(()=>{const n=e.default??null;C(n),u({value:n,source:{fromUi:!0}})},[e]);vt({element:e,widgetMgr:a,onFormCleared:st});const it=n=>{const{value:i}=n.target;if(i==="")T(!0),C(null),S(null);else{let d;e.dataType===y.DataType.INT?d=parseInt(i,10):d=parseFloat(i),T(!0),C(d),S(i)}},N=r.useCallback(()=>{x&&(T(!0),u({value:(l??h)+s,source:{fromUi:!0}}))},[l,h,s,x]),R=r.useCallback(()=>{V&&(T(!0),u({value:(l??g)-s,source:{fromUi:!0}}))},[l,g,s,V]),lt=r.useCallback(n=>{const{key:i}=n;switch(i){case"ArrowUp":n.preventDefault(),N();break;case"ArrowDown":n.preventDefault(),R();break}},[N,R]),ct=r.useCallback(n=>{n.key==="Enter"&&(I&&u({value:l,source:{fromUi:!0}}),a.allowFormEnterToSubmit(f)&&a.submitForm(f,b))},[I,l,u,a,f,b]),_=D?.startsWith(":material"),ut=_?"lg":"base",dt=G(o.iconSizes.lg)+2*G(o.spacing.twoXS),pt=D?o.breakpoints.hideNumberInputControls+dt:o.breakpoints.hideNumberInputControls;return z("div",{className:"stNumberInput","data-testid":"stNumberInput",ref:tt,children:[c(Ct,{label:e.label,disabled:t,labelVisibility:gt(e.labelVisibility?.value),htmlFor:$.current,children:e.help&&c(It,{children:c(yt,{content:e.help,placement:Tt.TOP_RIGHT})})}),z(Ft,{className:U?"focused":"","data-testid":"stNumberInputContainer",children:[c(kt,{type:"number",inputRef:v,value:P??"",placeholder:e.placeholder,onBlur:nt,onFocus:at,onChange:it,onKeyPress:ct,onKeyDown:lt,clearable:E,clearOnEscape:E,disabled:t,"aria-label":e.label,startEnhancer:e.icon&&c(wt,{"data-testid":"stNumberInputIcon",iconValue:e.icon,size:ut}),id:$.current,overrides:{ClearIconContainer:{style:{padding:0}},ClearIcon:{props:{overrides:{Svg:{style:{color:o.colors.darkGray,padding:o.spacing.threeXS,height:o.sizes.clearIconSize,width:o.sizes.clearIconSize,":hover":{fill:o.colors.bodyText}}}}}},Input:{props:{"data-testid":"stNumberInputField",step:s,min:h,max:g,type:"number",inputMode:""},style:{fontWeight:o.fontWeights.normal,lineHeight:o.lineHeights.inputWidget,paddingRight:o.spacing.sm,paddingLeft:o.spacing.md,paddingBottom:o.spacing.sm,paddingTop:o.spacing.sm,"::placeholder":{color:o.colors.fadedText60}}},InputContainer:{style:()=>({borderTopRightRadius:0,borderBottomRightRadius:0})},Root:{style:{borderTopRightRadius:0,borderBottomRightRadius:0,borderTopLeftRadius:0,borderBottomLeftRadius:0,borderLeftWidth:0,borderRightWidth:0,borderTopWidth:0,borderBottomWidth:0,paddingRight:0,paddingLeft:D?o.spacing.sm:0}},StartEnhancer:{style:{paddingLeft:0,paddingRight:0,minWidth:o.iconSizes.lg,color:_?o.colors.fadedText60:"inherit"}}}}),B>pt&&z(Wt,{children:[c(q,{"data-testid":"stNumberInputStepDown",onClick:R,disabled:!V||t,tabIndex:-1,children:c(X,{content:Y,size:"xs",color:V?"inherit":o.colors.disabled})}),c(q,{"data-testid":"stNumberInputStepUp",onClick:N,disabled:!x||t,tabIndex:-1,children:c(X,{content:Z,size:"xs",color:x?"inherit":o.colors.disabled})})]})]}),rt&&c(Lt,{clearable:E,children:c(Vt,{dirty:I,value:P??"",inForm:O,allowEnterToSubmit:ot})})]})},_t=r.memo(Bt);export{_t as default};
