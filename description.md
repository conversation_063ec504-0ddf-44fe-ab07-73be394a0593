# GPT-4o and Pinecone Integration Guide

## Module Overview

### GPT4oClient (`gpt4o_client.py`)
The `GPT4oClient` provides a streamlined interface for Azure OpenAI's GPT-4o model, supporting both streaming and non-streaming responses. It includes built-in RAG (Retrieval-Augmented Generation) context formatting and message management.

**Key Features:**
- Async/await support for non-blocking operations
- Streaming response generation
- RAG context integration
- Configurable temperature and token limits
- Error handling and recovery

### PineconeClient (`pinecone_client.py`)
The `PineconeClient` handles vector database operations for semantic search and RAG functionality. It provides embedding generation, vector storage, and similarity search capabilities.

**Key Features:**
- Azure OpenAI embedding integration
- Batch vector operations
- Async and sync query methods
- Namespace and metadata filtering
- Automatic error handling and fallbacks

## Architecture

```
User Query → PineconeClient → Vector Search → RAG Context
                                                    ↓
GPT-4o Response ← GPT4oClient ← Enhanced Prompt ← Context Formatting
```

The RAG workflow:
1. User submits a query
2. `PineconeClient` generates embeddings and searches for relevant context
3. Retrieved context is formatted and combined with the user query
4. `GPT4oClient` generates response using enhanced prompt with context
5. Response is returned to user (streaming or complete)

## Setup Instructions

### Dependencies

```bash
pip install openai pinecone-client python-dotenv asyncio
```

### Environment Variables

Create a `.env` file with the following configuration:

```env
# Azure OpenAI Configuration
AZURE_OPENAI_ENDPOINT=https://your-endpoint.openai.azure.com/
AZURE_OPENAI_API_KEY=your_api_key
AZURE_OPENAI_API_VERSION=2025-01-01-preview
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4.1

# Legacy Azure Configuration (for embeddings)
AZURE_API_KEY=your_legacy_api_key
EMBED_KEY=your_embed_key

# Pinecone Configuration
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_INDEX_NAME=your_index_name
PINECONE_NAMESPACE=your_namespace
RAG_TOP_K=5
```

### Initialization

```python
from gpt4o_client import GPT4oClient, initialize_gpt4o_client
from pinecone_client import PineconeClient

# Initialize clients
gpt4o_client = initialize_gpt4o_client()
pinecone_client = PineconeClient()
```

## Usage Examples

### Basic RAG Workflow

```python
import asyncio
from gpt4o_client import GPT4oClient
from pinecone_client import PineconeClient

async def rag_query_example():
    # Initialize clients
    gpt4o = GPT4oClient()
    pinecone = PineconeClient()
    
    # User query
    user_query = "What are the best investment strategies?"
    system_prompt = "You are a financial advisor. Provide helpful investment advice."
    
    # Step 1: Search for relevant context
    rag_context = await pinecone.search_rag_context_async(
        user_query=user_query,
        top_k=3
    )
    
    # Step 2: Generate response with context
    response = await gpt4o.generate_response(
        system_prompt=system_prompt,
        user_input=user_query,
        rag_context=rag_context,
        temperature=0.7
    )
    
    return response

# Run the example
result = asyncio.run(rag_query_example())
print(result)
```

### Streaming Response with RAG

```python
async def streaming_rag_example():
    gpt4o = GPT4oClient()
    pinecone = PineconeClient()
    
    user_query = "Explain portfolio diversification"
    system_prompt = "You are an investment expert."
    
    # Get RAG context
    context = pinecone.search_rag_context(user_query)
    
    # Stream response
    async for chunk in gpt4o.generate_streaming_response(
        system_prompt=system_prompt,
        user_input=user_query,
        rag_context=context
    ):
        print(chunk, end='', flush=True)

asyncio.run(streaming_rag_example())
```

### Vector Data Upload

```python
def upload_knowledge_base():
    pinecone = PineconeClient()
    
    # Prepare data for upload
    knowledge_data = [
        {
            "id": "doc_1",
            "value": "Portfolio diversification reduces investment risk...",
            "metadata": {
                "question": "What is portfolio diversification?",
                "answer": "Portfolio diversification reduces investment risk...",
                "category": "investment_strategy"
            }
        },
        # Add more documents...
    ]
    
    # Upload to Pinecone
    pinecone.upsert_vectors(
        index_name="investment-knowledge",
        namespace="financial_advice",
        embedded_data=knowledge_data
    )

upload_knowledge_base()
```

## API Reference

### GPT4oClient Methods

#### `generate_response(system_prompt, user_input, rag_context=None, temperature=0.7, max_tokens=4096)`
Generate complete response (non-streaming).

**Parameters:**
- `system_prompt` (str): System instructions
- `user_input` (str): User query
- `rag_context` (List[Dict], optional): RAG context from vector search
- `temperature` (float): Generation randomness (0.0-1.0)
- `max_tokens` (int): Maximum response length

**Returns:** `str` - Complete generated response

#### `generate_streaming_response(...)`
Generate streaming response with same parameters as above.

**Yields:** `str` - Text chunks as they're generated

### PineconeClient Methods

#### `search_rag_context(user_query, index_name=None, namespace=None, top_k=None)`
Search for relevant context based on user query.

**Parameters:**
- `user_query` (str): Search query
- `index_name` (str, optional): Pinecone index name
- `namespace` (str, optional): Pinecone namespace
- `top_k` (int, optional): Number of results

**Returns:** `List[Dict]` - Formatted context results

#### `upsert_vectors(index_name, namespace, embedded_data)`
Upload vectors to Pinecone index.

**Parameters:**
- `index_name` (str): Target index name
- `namespace` (str): Target namespace
- `embedded_data` (List[Dict]): Vector data with metadata

#### `query_vectors(query, index_name=None, namespace=None, metadata_filter=None, top_k=None)`
Raw vector similarity search.

**Returns:** `List[Dict]` - Raw Pinecone search results

## Integration Patterns

### 1. Basic RAG Pattern

```python
class RAGService:
    def __init__(self):
        self.gpt4o = GPT4oClient()
        self.pinecone = PineconeClient()
    
    async def answer_question(self, question: str, system_prompt: str = None):
        # Search for context
        context = await self.pinecone.search_rag_context_async(question)
        
        # Generate answer
        return await self.gpt4o.generate_response(
            system_prompt=system_prompt or "You are a helpful assistant.",
            user_input=question,
            rag_context=context
        )
```

### 2. Filtered Search Pattern

```python
async def domain_specific_rag(query: str, domain: str):
    pinecone = PineconeClient()
    gpt4o = GPT4oClient()
    
    # Search with metadata filter
    results = pinecone.query_vectors(
        query=query,
        metadata_filter={"category": domain},
        top_k=5
    )
    
    # Format context manually if needed
    context = [
        {
            "question": r["metadata"].get("question", ""),
            "answer": r["metadata"].get("answer", ""),
            "score": r.get("score", 0.0)
        }
        for r in results
    ]
    
    return await gpt4o.generate_response(
        system_prompt=f"You are an expert in {domain}.",
        user_input=query,
        rag_context=context
    )
```

### 3. Batch Processing Pattern

```python
async def process_multiple_queries(queries: List[str]):
    pinecone = PineconeClient()
    gpt4o = GPT4oClient()
    
    results = []
    for query in queries:
        context = await pinecone.search_rag_context_async(query)
        response = await gpt4o.generate_response(
            system_prompt="You are a helpful assistant.",
            user_input=query,
            rag_context=context
        )
        results.append({"query": query, "response": response})
    
    return results
```

## Performance Considerations

### Optimization Tips

1. **Connection Pooling**: Both clients use connection pooling internally
2. **Async Operations**: Use async methods for better concurrency
3. **Batch Operations**: Upload vectors in batches for better performance
4. **Caching**: Consider caching frequent embeddings
5. **Error Handling**: Implement retry logic for network failures

### Resource Management

```python
class OptimizedRAGService:
    def __init__(self):
        self.gpt4o = GPT4oClient()
        self.pinecone = PineconeClient()
        self._embedding_cache = {}
    
    async def cached_search(self, query: str):
        # Cache embeddings for repeated queries
        if query in self._embedding_cache:
            vector = self._embedding_cache[query]
        else:
            vector = await self.pinecone.async_embedder(query)
            self._embedding_cache[query] = vector
        
        # Use cached vector for search
        # ... implement custom search logic
```

## Error Handling Patterns

```python
async def robust_rag_query(query: str, max_retries: int = 3):
    gpt4o = GPT4oClient()
    pinecone = PineconeClient()
    
    for attempt in range(max_retries):
        try:
            # Try to get context
            context = await pinecone.search_rag_context_async(query)
            
            # Generate response
            response = await gpt4o.generate_response(
                system_prompt="You are a helpful assistant.",
                user_input=query,
                rag_context=context
            )
            
            return response
            
        except Exception as e:
            if attempt == max_retries - 1:
                # Final attempt failed, return fallback
                return f"Sorry, I encountered an error: {str(e)}"
            
            # Wait before retry
            await asyncio.sleep(2 ** attempt)
```

## Common Use Cases

1. **Customer Support**: RAG-powered chatbots with knowledge base search
2. **Document Q&A**: Query large document collections with context
3. **Educational Tools**: Interactive learning with curated content
4. **Research Assistance**: Academic paper search and summarization
5. **Technical Documentation**: Code and API documentation search

## Troubleshooting

### Common Issues

1. **Pinecone Connection Errors**: Check API key and network connectivity
2. **Azure OpenAI Rate Limits**: Implement exponential backoff
3. **Empty Context Results**: Verify index data and embedding model consistency
4. **Memory Issues**: Use streaming responses for large outputs

### Debug Mode

```python
import logging

# Enable debug logging
logging.basicConfig(level=logging.DEBUG)

# Check client status
gpt4o = GPT4oClient()
pinecone = PineconeClient()

print("GPT-4o Config:", gpt4o.get_client_info())
print("Pinecone Available:", pinecone._pinecone_available)
```