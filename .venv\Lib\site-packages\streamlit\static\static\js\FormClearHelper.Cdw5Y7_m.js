import{r as a,cb as i,Q as o}from"./index.CbQtRkVt.js";class l{manageFormClearListener(s,r,t){o(this.formClearListener)&&this.lastWidgetMgr===s&&this.lastFormId===r||(this.disconnect(),i(r)&&(this.formClearListener=s.addFormClearedListener(r,t),this.lastWidgetMgr=s,this.lastFormId=r))}disconnect(){this.formClearListener?.disconnect(),this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}}function d({element:e,widgetMgr:s,onFormCleared:r}){a.useEffect(()=>{if(!i(e.formId))return;const t=s.addFormClearedListener(e.formId,r);return()=>{t.disconnect()}},[e,s,r])}export{l as F,d as u};
