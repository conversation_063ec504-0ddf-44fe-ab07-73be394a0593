#!/usr/bin/env python3
"""
虛擬環境套件檢查腳本
檢查所有必要的套件是否正確安裝
"""

import sys
import subprocess

def check_package(package_name, import_name=None):
    """檢查套件是否安裝並可導入"""
    if import_name is None:
        import_name = package_name
    
    try:
        # 檢查是否可以導入
        __import__(import_name)
        
        # 檢查版本
        try:
            result = subprocess.run([sys.executable, '-m', 'pip', 'show', package_name], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                version_line = [line for line in result.stdout.split('\n') if line.startswith('Version:')]
                version = version_line[0].split(': ')[1] if version_line else 'Unknown'
                return True, version
            else:
                return True, 'Unknown'
        except:
            return True, 'Unknown'
            
    except ImportError:
        return False, None

def main():
    print("🔍 檢查虛擬環境套件安裝狀態")
    print("=" * 50)
    
    # Python 版本
    print(f"Python 版本: {sys.version.split()[0]}")
    print()
    
    # 必要套件清單
    required_packages = [
        ('PyPDF2', 'PyPDF2'),
        ('langchain-text-splitters', 'langchain_text_splitters'),
        ('pinecone', 'pinecone'),
        ('openai', 'openai'),
        ('python-dotenv', 'dotenv'),
    ]
    
    all_installed = True
    
    for package_name, import_name in required_packages:
        installed, version = check_package(package_name, import_name)
        
        if installed:
            print(f"✅ {package_name:<25} 版本: {version}")
        else:
            print(f"❌ {package_name:<25} 未安裝")
            all_installed = False
    
    print()
    print("=" * 50)
    
    if all_installed:
        print("🎉 所有必要套件都已正確安裝！")
        
        # 測試關鍵功能
        print("\n🧪 測試關鍵功能...")
        
        try:
            from langchain_text_splitters import RecursiveCharacterTextSplitter
            splitter = RecursiveCharacterTextSplitter(chunk_size=100)
            print("✅ 文本分割器: 正常")
        except Exception as e:
            print(f"❌ 文本分割器: {e}")
        
        try:
            from pinecone import Pinecone
            print("✅ Pinecone 客戶端: 正常")
        except Exception as e:
            print(f"❌ Pinecone 客戶端: {e}")
        
        try:
            import openai
            print("✅ OpenAI 客戶端: 正常")
        except Exception as e:
            print(f"❌ OpenAI 客戶端: {e}")
            
        print("\n✅ 環境檢查完成，可以運行 main.py")
        
    else:
        print("⚠️  有套件未安裝，請運行以下命令安裝:")
        print("pip install -r requirements.txt")

if __name__ == "__main__":
    main()
