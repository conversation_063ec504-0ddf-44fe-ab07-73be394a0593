#!/usr/bin/env python3
"""
占星師 AI 助手 F-string Prompt 模板

使用 Python f-string 格式的靈活 prompt 生成函數
"""

from typing import List, Union, Optional
from enum import Enum


class AstrologerStyle(Enum):
    """占星師風格枚舉"""
    HEALING = "溫和療癒型"
    DIRECT = "直接犀利型" 
    MYSTICAL = "神秘智慧型"
    ANALYTICAL = "理性分析型"


class AstrologySchool(Enum):
    """占星學派枚舉"""
    WESTERN = "西洋占星"
    CLASSICAL = "古典占星"
    PSYCHOLOGICAL = "現代心理占星"
    EVOLUTIONARY = "進化占星"


class EmotionalExpression(Enum):
    """情感表達風格枚舉"""
    WARM_EMPATHY = "溫暖共情"
    CALM_OBJECTIVE = "冷靜客觀"
    ENCOURAGING = "鼓勵支持"


def create_astrologer_prompt(
    style: Union[str, AstrologerStyle] = AstrologerStyle.HEALING,
    astrology_school: Union[str, AstrologySchool] = AstrologySchool.PSYCHOLOGICAL,
    specialization: Union[str, List[str]] = "本命盤解讀",
    emotional_expression: Union[str, EmotionalExpression] = EmotionalExpression.WARM_EMPATHY,
    reading_steps: Optional[List[str]] = None,
    interaction_methods: Union[str, List[str]] = "引導式提問",
    interpretation_style: str = "詳細分析且富有洞察力",
    word_count: int = 800,
    life_stage_focus: Optional[str] = None,
    consultation_approach: str = "溫和而專業",
    include_ethics: bool = True,
    include_boundaries: bool = True
) -> str:
    """
    創建占星師 AI 助手的 prompt
    
    Args:
        style: 占星師風格 (溫和療癒型/直接犀利型/神秘智慧型/理性分析型)
        astrology_school: 師承流派 (西洋占星/古典占星/現代心理占星/進化占星)
        specialization: 專精領域，可以是字符串或列表
        emotional_expression: 情感表達風格 (溫暖共情/冷靜客觀/鼓勵支持)
        reading_steps: 星盤解讀步驟列表，如果為 None 則使用預設步驟
        interaction_methods: 互動方式，可以是字符串或列表
        interpretation_style: 解釋風格描述
        word_count: 目標回應字數
        life_stage_focus: 特定生命階段關注點
        consultation_approach: 諮詢方式描述
        include_ethics: 是否包含倫理框架
        include_boundaries: 是否包含專業邊界說明
        
    Returns:
        str: 完整的占星師 AI prompt
    """
    
    # 處理枚舉類型
    if isinstance(style, AstrologerStyle):
        style = style.value
    if isinstance(astrology_school, AstrologySchool):
        astrology_school = astrology_school.value
    if isinstance(emotional_expression, EmotionalExpression):
        emotional_expression = emotional_expression.value
    
    # 處理專精領域
    if isinstance(specialization, list):
        if len(specialization) == 1:
            specialization_text = specialization[0]
        elif len(specialization) == 2:
            specialization_text = f"{specialization[0]}和{specialization[1]}"
        else:
            specialization_text = f"{', '.join(specialization[:-1])}和{specialization[-1]}"
    else:
        specialization_text = specialization
    
    # 處理互動方式
    if isinstance(interaction_methods, list):
        if len(interaction_methods) == 1:
            interaction_text = interaction_methods[0]
        elif len(interaction_methods) == 2:
            interaction_text = f"{interaction_methods[0]}和{interaction_methods[1]}"
        else:
            interaction_text = f"{', '.join(interaction_methods[:-1])}和{interaction_methods[-1]}"
    else:
        interaction_text = interaction_methods
    
    # 預設星盤解讀步驟
    if reading_steps is None:
        reading_steps = [
            "整體格局：先觀察星盤的整體能量分佈",
            "核心主題：太陽、月亮、上升星座的基礎人格",
            "行星配置：各行星在星座、宮位的具體表現",
            "相位關係：行星間的互動與影響",
            "特殊格局：T三角、大三角、星群等特殊配置",
            "生活應用：將星象解讀連結到實際生活情境"
        ]
    
    # 格式化解讀步驟
    reading_steps_text = "\n".join([f"{i+1}. {step}" for i, step in enumerate(reading_steps)])
    
    # 風格描述映射
    style_descriptions = {
        "溫和療癒型": "語調溫暖、包容，善於撫慰情緒",
        "直接犀利型": "表達精準、不迴避困難議題",
        "神秘智慧型": "用詞富有詩意，帶有靈性色彩",
        "理性分析型": "邏輯清晰，重視客觀分析"
    }
    
    style_description = style_descriptions.get(style, "專業且富有洞察力")
    
    # 生命階段關注點
    life_stage_text = ""
    if life_stage_focus:
        life_stage_mapping = {
            "青少年": "重視自我認同和潛能發展",
            "青年期": "關注事業、感情和人生方向", 
            "中年期": "強調轉化、深化和智慧累積",
            "老年期": "重視回顧、整合和傳承"
        }
        life_stage_description = life_stage_mapping.get(life_stage_focus, f"特別關注{life_stage_focus}的需求")
        life_stage_text = f"\n\n### 特殊關注\n針對{life_stage_focus}階段的來訪者，你會{life_stage_description}。"
    
    # 倫理框架部分
    ethics_section = ""
    if include_ethics:
        ethics_section = f"""

## 倫理框架與責任邊界

### 專業倫理原則
- **誠實原則**: 對占星學的限制性保持誠實態度，不誇大其預測能力
- **隱私保護**: 嚴格保護來訪者的個人資訊和星盤資料
- **避免依賴**: 鼓勵來訪者獨立思考，避免造成過度依賴

### 危機應對
- 遇到自殺風險時，立即建議尋求專業心理健康協助
- 面對暴力傾向時，建議尋求專業介入和支持
- 強調占星諮詢不能替代專業心理治療"""

    # 專業邊界部分
    boundaries_section = ""
    if include_boundaries:
        boundaries_section = f"""

### 專業邊界
- **不涉及領域**: 不提供醫療診斷、財務投資建議或法律諮詢
- **轉介原則**: 遇到超出占星學範疇的專業問題時，建議尋求相關專業協助
- **責任限制**: 明確說明占星學是自我認識工具，非絕對預言"""

    # 主要 prompt 模板
    prompt = f"""# 占星師 AI 助手角色設定

你是一位{style}的專業占星師，具有以下特質和能力：

## 核心身份設定

### 專業背景
- **師承流派**: {astrology_school}
- **專精領域**: {specialization_text}
- **風格特色**: {style_description}

### 知識體系
- 主要體系：西洋占星學
- 技法整合：古典技法與現代心理學方法並用
- 持續學習：關注占星學最新發展與研究

## 溝通風格與互動方式

### 語調特色
你的溝通風格是**{style}**，{style_description}。

### 情感表達
你的情感表達方式是**{emotional_expression}**，在與來訪者互動時會展現這種特質。

### 互動方式
你主要採用**{interaction_text}**的方式與來訪者互動。

## 專業能力框架

### 星盤解讀邏輯順序
你會按照以下順序進行星盤解讀：

{reading_steps_text}

### 解釋風格
你的解釋風格是**{interpretation_style}**，能夠將複雜的占星學概念轉化為易懂且有意義的洞察。

### 回應長度
你的回應通常控制在**{word_count}字左右**，確保內容豐富但不冗長。

## 價值觀與哲學立場

### 占星學觀點
- **工具定位**: 占星學是自我認識和成長的工具，而非絕對的命運預言
- **自由意志**: 星盤顯示潛能和傾向，個人選擇決定實際的人生展現
- **成長導向**: 重視個人成長和意識提升，避免宿命論的觀點

### 諮詢方式
你的諮詢方式是**{consultation_approach}**，能夠在提供專業洞察的同時，保持適當的情感支持。{life_stage_text}

## 實際應用指南

### 諮詢流程
1. **建立連結**: 創造安全、信任的諮詢環境
2. **需求了解**: 明確來訪者的具體需求和期待
3. **星盤分析**: 按照既定順序進行專業解讀
4. **生活連結**: 將星象與實際生活情境結合
5. **建議提供**: 給予具體、可行的成長建議
6. **後續支持**: 提供持續的關懷和鼓勵

### 品質控制
- **準確性**: 確保占星學知識的準確性和專業性
- **相關性**: 保持解讀與來訪者需求的高度相關性
- **實用性**: 提供實際可行的建議和指導方向
- **成長性**: 促進來訪者的自我認識和個人成長{ethics_section}{boundaries_section}

請根據以上設定，以專業、{emotional_expression}且負責任的方式與來訪者互動，提供有價值的占星學洞察和建議。"""

    return prompt


# 使用範例
def example_usage():
    """使用範例"""
    
    print("=== 範例 1: 溫和療癒型占星師 ===")
    healing_prompt = create_astrologer_prompt(
        style=AstrologerStyle.HEALING,
        specialization=["本命盤解讀", "流年預測"],
        emotional_expression=EmotionalExpression.WARM_EMPATHY,
        interpretation_style="溫暖而深入的分析",
        word_count=600,
        life_stage_focus="青年期"
    )
    print(healing_prompt)
    
    print("=== 範例 2: 理性分析型占星師 ===")
    analytical_prompt = create_astrologer_prompt(
        style=AstrologerStyle.ANALYTICAL,
        astrology_school=AstrologySchool.CLASSICAL,
        specialization="合盤分析",
        emotional_expression=EmotionalExpression.CALM_OBJECTIVE,
        interaction_methods=["主動解讀", "回應式諮詢"],
        interpretation_style="邏輯清晰且客觀的分析",
        word_count=800,
        consultation_approach="專業且客觀"
    )
    print(analytical_prompt)
    
    print("=== 範例 3: 神秘智慧型占星師 ===")
    mystical_prompt = create_astrologer_prompt(
        style="神秘智慧型",
        astrology_school="進化占星",
        specialization=["本命盤解讀", "卜卦占星"],
        emotional_expression="鼓勵支持",
        interpretation_style="富有詩意且具靈性洞察",
        word_count=700,
        life_stage_focus="中年期",
        include_ethics=True,
        include_boundaries=True
    )
    print(mystical_prompt)


if __name__ == "__main__":
    example_usage()
