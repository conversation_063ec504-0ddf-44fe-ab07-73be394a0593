專案規格與功能說明
整體架構
這是一個占星學知識庫的RAG（Retrieval-Augmented Generation）系統，主要功能是：

將PDF占星學資料轉換為向量資料庫
提供有RAG增強和無RAG增強的兩種AI回答模式進行比較
核心模組規格
1. chunk.py（待開發）
目的：將PDF資料分割成適合embedding的文本塊

功能：
使用 RecursiveCharacterTextSplitter 進行文本分割
每個chunk約200-300字
按段落進行智能分割
生成適當的metadata（包含來源，章節名稱，內文關鍵字等資訊）
輸入：PDF文件內容
輸出：分割後的文本塊列表，每個包含text和metadata
2. main.py（待開發）
目的：主要執行流程控制

功能流程：
讀取 /data 目錄中的PDF文件
調用 chunk.py 將PDF轉換為chunks並建立metadata
使用 pinecone_client.py 進行embedding和向量資料庫upsert
使用 gpt4o_client.py 創建兩個client實例：
有RAG的client：結合向量搜尋結果回答問題
無RAG的client：僅使用基礎模型回答問題
測試prompt：「一個金星在射手的人會是什麼樣的人呢？」
測試prompt：「月亮處女與月亮射手的人分別是什麼樣的人呢？」

比較兩種回答的差異
3. pinecone_client.py（已存在）
現有功能：

向量embedding生成
Pinecone向量資料庫操作（upsert、query）
RAG上下文搜尋功能
4. gpt4o_client.py（已存在）
現有功能：

GPT-4o模型調用
支援RAG上下文整合
串流和非串流回應生成
資料來源
/data 目錄中的PDF文件：
Demetra-George-Ancient-Astrology-in-Theory-and-Practice...pdf
The Twelve Houses_ Understanding the Importance of the 12...pdf
預期輸出
處理報告：顯示PDF處理進度、chunk數量、embedding成功率
RAG vs 非RAG比較：
無RAG回答：僅基於模型訓練知識
有RAG回答：結合PDF知識庫的專業回答
顯示相關度分數和引用來源