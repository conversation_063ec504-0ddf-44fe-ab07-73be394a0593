# 🌟 AI Astrologer Streamlit Demo

A simple, comprehensive web interface showcasing the RAG-enhanced AI astrologer system built with Streamlit.

## ✨ Features

### 🔮 **Interactive Chat Interface**
- Real-time astrology consultations
- Professional astrologer persona responses
- Chat history with timestamps

### 📊 **RAG Visualization & Analysis**
- Live RAG retrieval statistics
- Similarity score monitoring
- Fallback behavior demonstration
- Knowledge source transparency

### 🔍 **Complete Prompt Transparency**
- **Sectioned View**: See base persona, RAG context, and user question separately
- **Complete Prompt View**: View the full system prompt sent to GPT-4
- **Statistics**: Word count, token estimates, character count
- **Real-time Updates**: Prompt changes with each query

### ⚙️ **System Configuration**
- Adjustable similarity threshold (0.0 - 1.0)
- RAG enable/disable toggle
- Real-time system status monitoring
- Performance metrics display

## 🚀 Quick Start

### Option 1: Using the Launcher Script
```bash
python run_streamlit_demo.py
```

### Option 2: Direct Streamlit Command
```bash
# Install Streamlit if needed
pip install streamlit>=1.28.0

# Run the demo
streamlit run streamlit_astrologer_demo.py
```

### Option 3: Test First, Then Run
```bash
# Validate everything works
python test_streamlit_demo.py

# If tests pass, run the demo
python run_streamlit_demo.py
```

## 🖥️ Interface Overview

### **Sidebar Controls**
- **Similarity Threshold Slider**: Adjust RAG filtering sensitivity
- **RAG Toggle**: Enable/disable retrieval-augmented generation
- **Initialize Button**: Start the astrologer system
- **System Status**: Real-time connection and performance info

### **Main Interface Panels**

#### 1. **Chat Interface** (Left Column)
- Question input field
- AI astrologer responses
- Chat history with expandable entries

#### 2. **RAG Analysis** (Right Column)
- Matches found vs. matches used
- Current similarity threshold
- RAG active/fallback status indicators
- Retrieved knowledge context display

#### 3. **Prompt Transparency** (Full Width)
- **📋 Sections Tab**: 
  - 🎭 Base Astrologer Persona
  - 🔍 RAG Context (when available)
  - ❓ User Question
- **📄 Complete Prompt Tab**: Full system prompt
- **📊 Statistics**: Word count, tokens, characters

## 🎯 Demo Scenarios

### **Scenario 1: RAG Active** (Low Threshold)
1. Set similarity threshold to 0.5-0.6
2. Ask: "What does Mars in the 7th house mean?"
3. Observe: RAG chunks retrieved and used in response

### **Scenario 2: Fallback Mode** (High Threshold)
1. Set similarity threshold to 0.8-0.9
2. Ask: "How do Saturn transits affect personal growth?"
3. Observe: No RAG chunks meet threshold, fallback to base knowledge

### **Scenario 3: Prompt Transparency**
1. Ask any astrology question
2. Check the "Prompt Transparency" section
3. Compare "Sections" vs "Complete Prompt" views
4. Note how RAG context integrates with base persona

## 🔧 Technical Details

### **Architecture**
- **Frontend**: Streamlit (Python-based web framework)
- **Backend**: Direct integration with `astrologer_prototype.py`
- **RAG System**: Pinecone vector database + OpenAI embeddings
- **LLM**: GPT-4 via Azure OpenAI

### **Key Components**
- `streamlit_astrologer_demo.py` - Main Streamlit application
- `astrologer_prototype.py` - Core AI astrologer system
- `run_streamlit_demo.py` - Launcher script with auto-installation
- `test_streamlit_demo.py` - Validation test suite

### **Configuration**
- **Pinecone Index**: "astrology-text"
- **Namespace**: "hierarchical_chunking_strategy"
- **Default Threshold**: 0.7
- **Port**: 8501 (Streamlit default)

## 🎨 Visual Features

### **Astrology Theme**
- Mystical color scheme with blues and purples
- Cosmic emojis and astrology symbols
- Clean, professional layout
- Responsive design for different screen sizes

### **Interactive Elements**
- Expandable sections for detailed information
- Tabbed interface for different prompt views
- Real-time metrics and status indicators
- Copy-friendly text areas for prompt inspection

## 🔍 Understanding the System

### **RAG Pipeline Visualization**
The interface shows the complete RAG pipeline in action:
1. **User Query** → Embedding generation
2. **Vector Search** → Pinecone retrieval
3. **Similarity Filtering** → Threshold application
4. **Context Injection** → Prompt assembly
5. **LLM Generation** → AI astrologer response

### **Prompt Transparency Benefits**
- **Educational**: See how RAG enhances AI responses
- **Debugging**: Understand why certain responses are generated
- **Trust**: Complete transparency in AI decision-making
- **Optimization**: Adjust thresholds based on prompt quality

## 🚀 Next Steps

This Streamlit demo provides a solid foundation for:
- **User Testing**: Gather feedback on RAG effectiveness
- **System Tuning**: Optimize similarity thresholds
- **Feature Development**: Add new astrology-specific features
- **Integration**: Connect with additional data sources

The simple, maintainable design makes it easy to extend with new features while keeping the core functionality clear and accessible.

## 🎉 Success Metrics

The demo successfully demonstrates:
- ✅ Complete RAG pipeline functionality
- ✅ Similarity threshold filtering with fallback
- ✅ Prompt transparency and system understanding
- ✅ Professional astrologer persona integration
- ✅ Real-time system monitoring and configuration
- ✅ User-friendly interface for non-technical users
