---
type: "manual"
---

Operation rules
All actions should do under a virtual environment. 
Do not use global environment.

Naming Conventions

Use meaningful variable and function names, avoid single letters (except loop counters i, j, k)
Functions use snake_case, classes use PascalCase
Constants use UPPER_CASE

Code Structure

Each function should do one thing and stay under 20 lines
Use type hints for parameters and return values
Write concise docstrings at the beginning of functions

Error Handling

Add appropriate try-except blocks for operations that may fail
Use specific exception types, avoid bare except
Log or re-raise meaningful error messages

Code Quality

Follow PEP 8 formatting standards
Remove unused imports and variables
Prefer built-in functions and standard library
Avoid deep nesting, refactor if more than 3 levels

Comment Guidelines

Explain "why" not "what"
Add comments for complex logic
Avoid obvious comments

Example Format
pythondef calculate_discount(price: float, discount_rate: float) -> float:
    """Calculate discounted price"""
    if not 0 <= discount_rate <= 1:
        raise ValueError("Discount rate must be between 0 and 1")
    
    return price * (1 - discount_rate)