"""
PDF文本分割模組 - 分層分塊策略
目的：將PDF資料分割成適合embedding的文本塊
實現段落級別上下文 + 句子級別embedding的分層策略
"""

import os
import hashlib
from typing import List, Dict, Optional
from datetime import datetime
import re

try:
    import PyPDF2
except ImportError:
    print("Warning: PyPDF2 not installed. Please install with: pip install PyPDF2")
    PyPDF2 = None

try:
    from langchain_text_splitters import RecursiveCharacterTextSplitter
except ImportError:
    print("Warning: langchain_text_splitters not installed. Please install with: pip install langchain-text-splitters")
    RecursiveCharacterTextSplitter = None


class HierarchicalPDFChunker:
    """
    分層PDF文本分割器
    
    實現兩層分塊策略：
    1. 段落級別：提取完整段落作為上下文
    2. 句子級別：將段落分割成句子進行embedding
    """
    
    def __init__(self, use_hierarchical: bool = True):
        """
        初始化分層PDF分割器
        
        Args:
            use_hierarchical (bool): 是否使用分層策略
        """
        self.use_hierarchical = use_hierarchical
        
        if RecursiveCharacterTextSplitter is None:
            raise ImportError("langchain_text_splitters is required. Please install with: pip install langchain-text-splitters")
        
        # 句子級別分割器
        self.sentence_splitter = RecursiveCharacterTextSplitter(
            chunk_size=200,
            chunk_overlap=20,
            length_function=len,
            separators=[". ", "! ", "? ", "; ", "\n", " ", ""]
        )
        
        # 傳統分割器（備用）
        self.traditional_splitter = RecursiveCharacterTextSplitter(
            chunk_size=250,
            chunk_overlap=50,
            length_function=len,
            separators=["\n\n", "\n", ". ", "! ", "? ", "; ", ", ", " ", ""]
        )
    
    def load_pdf(self, file_path: str) -> str:
        """讀取PDF文件內容"""
        if PyPDF2 is None:
            raise ImportError("PyPDF2 is required. Please install with: pip install PyPDF2")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"PDF文件不存在: {file_path}")
        
        text_content = ""
        
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                print(f"正在讀取PDF: {os.path.basename(file_path)}")
                print(f"總頁數: {len(pdf_reader.pages)}")
                
                for page_num, page in enumerate(pdf_reader.pages, 1):
                    try:
                        page_text = page.extract_text()
                        if page_text.strip():
                            text_content += f"\n\n--- 第{page_num}頁 ---\n\n"
                            text_content += page_text
                        
                        if page_num % 10 == 0:
                            print(f"已處理 {page_num} 頁...")
                            
                    except Exception as e:
                        print(f"警告: 第{page_num}頁讀取失敗: {str(e)}")
                        continue
                
                print(f"PDF讀取完成，總字符數: {len(text_content)}")
                
        except Exception as e:
            raise Exception(f"PDF讀取失敗: {str(e)}")
        
        return text_content
    
    def extract_paragraphs(self, text: str) -> List[Dict]:
        """提取段落並分析段落級別的上下文"""
        # 按雙換行符分割段落
        raw_paragraphs = text.split('\n\n')
        
        paragraphs = []
        for i, para_text in enumerate(raw_paragraphs):
            clean_para = para_text.strip()
            
            # 跳過太短的段落或頁面標記
            if (len(clean_para) < 20 or 
                re.match(r'^--- 第\d+頁 ---$', clean_para) or
                clean_para.isdigit()):
                continue
            
            # 提取段落關鍵字
            para_keywords = self.extract_keywords(clean_para, max_keywords=3)
            
            # 判斷段落類型
            para_type = self.classify_paragraph_type(clean_para)
            
            # 提取主題
            themes = self.extract_themes(clean_para)
            
            paragraph_data = {
                "paragraph_id": i,
                "text": clean_para,
                "char_count": len(clean_para),
                "sentence_count": len(re.findall(r'[.!?]+', clean_para)),
                "keywords": para_keywords,
                "paragraph_type": para_type,
                "themes": themes
            }
            
            paragraphs.append(paragraph_data)
        
        return paragraphs
    
    def extract_keywords(self, text: str, max_keywords: int = 5) -> List[str]:
        """提取英文關鍵字"""
        # 移除標點符號，保留字母和數字
        clean_text = re.sub(r'[^\w\s]', ' ', text)
        
        # 分詞
        words = clean_text.lower().split()
        
        # 英文停用詞
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
            'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
            'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must', 'shall',
            'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they',
            'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their',
            'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below',
            'not', 'no', 'nor', 'so', 'than', 'too', 'very', 'just', 'now', 'here', 'there', 'when', 'where',
            'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such'
        }
        keywords = [word for word in words if len(word) > 2 and word not in stop_words and word.isalpha()]
        
        # 統計詞頻並取前N個
        word_freq = {}
        for word in keywords:
            word_freq[word] = word_freq.get(word, 0) + 1
        
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return [word for word, _ in sorted_words[:max_keywords]]
    
    def classify_paragraph_type(self, paragraph: str) -> str:
        """分類段落類型"""
        text_upper = paragraph[:100].upper()
        
        if (re.search(r'CHAPTER\s+\d+|PART\s+\d+|SECTION\s+\d+', text_upper) or
            re.search(r'^\d+\.\s*[A-Z]', paragraph[:50]) or
            (len(paragraph) < 100 and paragraph.isupper())):
            return "title"
        
        if re.search(r'^\s*[-•*]\s+', paragraph, re.MULTILINE):
            return "list"
        
        if paragraph.startswith('"') or paragraph.count('"') >= 2:
            return "quote"
        
        if "Table of Contents" in paragraph[:100] or "INDEX" in text_upper:
            return "index"
        
        return "content"
    
    def extract_themes(self, paragraph: str) -> List[str]:
        """提取段落主題"""
        themes = []
        
        # 占星學相關主題檢測
        astrology_patterns = {
            "planets": r'\b(sun|moon|mercury|venus|mars|jupiter|saturn|uranus|neptune|pluto)\b',
            "signs": r'\b(aries|taurus|gemini|cancer|leo|virgo|libra|scorpio|sagittarius|capricorn|aquarius|pisces)\b',
            "houses": r'\b(first|second|third|fourth|fifth|sixth|seventh|eighth|ninth|tenth|eleventh|twelfth)\s+house\b',
            "aspects": r'\b(conjunction|opposition|trine|square|sextile|quincunx)\b',
            "elements": r'\b(fire|earth|air|water)\s+(sign|element)\b',
            "modalities": r'\b(cardinal|fixed|mutable)\b'
        }
        
        text_lower = paragraph.lower()
        for theme, pattern in astrology_patterns.items():
            if re.search(pattern, text_lower, re.IGNORECASE):
                themes.append(theme)
        
        return themes
    
    def split_paragraph_to_sentences(self, paragraph_data: Dict) -> List[Dict]:
        """將段落分割成句子級別的chunks"""
        paragraph_text = paragraph_data["text"]
        
        # 使用句子分割器
        sentences = self.sentence_splitter.split_text(paragraph_text)
        
        sentence_chunks = []
        for i, sentence in enumerate(sentences):
            clean_sentence = sentence.strip()
            
            if len(clean_sentence) < 15:
                continue
            
            # 創建句子級別的chunk
            sentence_chunk = {
                "text": clean_sentence,
                "sentence_index": i,
                "sentence_position": f"{i+1}/{len(sentences)}",
                "paragraph_context": paragraph_text,
                "paragraph_id": paragraph_data["paragraph_id"],
                "paragraph_metadata": {
                    "keywords": paragraph_data["keywords"],
                    "themes": paragraph_data["themes"],
                    "paragraph_type": paragraph_data["paragraph_type"],
                    "char_count": paragraph_data["char_count"],
                    "sentence_count": paragraph_data["sentence_count"]
                }
            }
            
            sentence_chunks.append(sentence_chunk)
        
        return sentence_chunks
