#!/usr/bin/env python3
"""
Test script to validate the conversation functionality in the Streamlit demo.
"""

import asyncio
import sys
import os

# Add current directory to path
sys.path.insert(0, os.getcwd())

def test_conversation_functions():
    """Test the conversation management functions."""
    print("🧪 Testing conversation management functions...")
    
    try:
        from streamlit_astrologer_demo import add_to_conversation, clear_conversation
        print("✅ Conversation functions imported successfully")
        
        # Mock session state for testing
        class MockSessionState:
            def __init__(self):
                self.conversation_history = []
                self.input_key = 0
        
        # Create mock session state
        mock_session = MockSessionState()
        
        # Mock result data
        mock_result = {
            'response': 'This is a test astrology response about Mars in the 7th house.',
            'rag_info': {
                'matches_found': 5,
                'matches_used': 3,
                'enabled': True
            },
            'similarity_threshold': 0.7,
            'has_rag_context': True,
            'prompt_data': {
                'base_persona': 'Test persona',
                'rag_context': 'Test RAG context',
                'user_question': 'Test question',
                'final_system_prompt': 'Test prompt',
                'word_count': 100,
                'estimated_tokens': 150
            }
        }
        
        print("✅ Mock data created successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error testing conversation functions: {e}")
        return False

async def test_astrologer_integration():
    """Test that the astrologer still works with conversation features."""
    print("\n🔮 Testing astrologer integration with conversation features...")
    
    try:
        from astrologer_prototype import AstrologerPrototype
        
        # Initialize astrologer
        astrologer = AstrologerPrototype(similarity_threshold=0.7)
        print("✅ Astrologer initialized")
        
        # Test multiple questions to simulate conversation
        questions = [
            "What does Mars in the 7th house mean?",
            "How does this affect relationships?",
            "What about Venus aspects?"
        ]
        
        conversation_results = []
        
        for i, question in enumerate(questions, 1):
            print(f"   Processing question {i}: {question[:30]}...")
            
            result = await astrologer.get_astrology_response(question, enable_rag=True)
            
            if result:
                conversation_results.append({
                    'question': question,
                    'result': result
                })
                print(f"   ✅ Got response {i} ({len(result['response'])} chars)")
            else:
                print(f"   ❌ Failed to get response {i}")
                return False
        
        print(f"✅ Successfully processed {len(conversation_results)} conversation exchanges")
        
        # Verify prompt data is included
        for i, exchange in enumerate(conversation_results, 1):
            if 'prompt_data' in exchange['result']:
                print(f"   ✅ Exchange {i} has prompt transparency data")
            else:
                print(f"   ❌ Exchange {i} missing prompt data")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing astrologer integration: {e}")
        return False

def test_streamlit_structure():
    """Test that the Streamlit app has the required conversation components."""
    print("\n📄 Testing Streamlit app conversation structure...")
    
    if not os.path.exists("streamlit_astrologer_demo.py"):
        print("❌ streamlit_astrologer_demo.py not found")
        return False
    
    try:
        with open("streamlit_astrologer_demo.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for conversation-specific components
        required_components = [
            "conversation_history",
            "add_to_conversation",
            "clear_conversation", 
            "display_conversation_history",
            "session_id",
            "input_key",
            "st.form",
            "st.rerun",
            "tab1, tab2, tab3 = st.tabs"
        ]
        
        for component in required_components:
            if component in content:
                print(f"✅ Found: {component}")
            else:
                print(f"❌ Missing: {component}")
                return False
        
        print("✅ Streamlit conversation structure looks good")
        return True
        
    except Exception as e:
        print(f"❌ Error reading Streamlit app: {e}")
        return False

def main():
    """Run all conversation tests."""
    print("🌟 AI Astrologer Conversation Demo - Test Suite")
    print("=" * 70)
    
    # Test conversation functions
    if not test_conversation_functions():
        print("\n❌ Conversation function tests failed.")
        return
    
    # Test Streamlit app structure
    if not test_streamlit_structure():
        print("\n❌ Streamlit conversation structure tests failed.")
        return
    
    # Test astrologer integration
    success = asyncio.run(test_astrologer_integration())
    
    if success:
        print("\n" + "=" * 70)
        print("🎉 All conversation tests passed! The enhanced demo should work correctly.")
        print("\n🚀 New Features Available:")
        print("   ✅ Persistent conversation history")
        print("   ✅ Continuous chat interface")
        print("   ✅ Session state management")
        print("   ✅ Multi-tab interface")
        print("   ✅ Real-time conversation display")
        print("   ✅ Clear conversation functionality")
        print("   ✅ Random question suggestions")
        print("\n🌟 To run the enhanced demo:")
        print("   python run_streamlit_demo.py")
        print("   OR")
        print("   streamlit run streamlit_astrologer_demo.py")
    else:
        print("\n❌ Some conversation tests failed. Please check the configuration.")

if __name__ == "__main__":
    main()
