import{r as p,z as k,B as T,j as n,C,br as y,aH as L,bz as S,bs as W,b9 as B,bL as w,J as R,bM as E,bN as $}from"./index.CbQtRkVt.js";import{a as P}from"./useBasicWidgetState.Bx3VaRHk.js";import{a as v,L as H,S as m}from"./checkbox.C7QR6llE.js";import"./FormClearHelper.Cdw5Y7_m.js";function M({element:e,disabled:a,widgetMgr:s,fragmentId:d}){const[f,g]=P({getStateFromWidgetMgr:X,getDefaultStateFromProto:V,getCurrStateFromProto:z,updateWidgetMgrState:I,element:e,widgetMgr:s,fragmentId:d}),x=p.useCallback(i=>{g({value:i.target.checked,fromUi:!0})},[g]),t=k(),{colors:o,spacing:h,sizes:r}=t,b=T(t),u=a?o.fadedText40:o.bodyText;return n($,{className:"row-widget stCheckbox","data-testid":"stCheckbox",children:n(v,{checked:f,disabled:a,onChange:x,"aria-label":e.label,checkmarkType:e.type===E.StyleType.TOGGLE?m.toggle:m.default,labelPlacement:H.right,overrides:{Root:{style:({$isFocusVisible:i})=>({marginBottom:h.none,marginTop:h.none,backgroundColor:i?o.darkenedBgMix25:"",display:"flex",alignItems:"start"})},Toggle:{style:({$checked:i})=>{let c=b?o.bgColor:o.bodyText;return a&&(c=b?o.gray70:o.gray90),{width:`calc(${r.checkbox} - ${t.spacing.twoXS})`,height:`calc(${r.checkbox} - ${t.spacing.twoXS})`,transform:i?`translateX(${r.checkbox})`:"",backgroundColor:c,boxShadow:""}}},ToggleTrack:{style:({$checked:i,$isHovered:c})=>{let l=o.fadedText40;return c&&!a&&(l=o.fadedText20),i&&!a&&(l=o.primary),{marginRight:0,marginLeft:0,marginBottom:0,marginTop:t.spacing.twoXS,paddingLeft:t.spacing.threeXS,paddingRight:t.spacing.threeXS,width:`calc(2 * ${r.checkbox})`,minWidth:`calc(2 * ${r.checkbox})`,height:r.checkbox,minHeight:r.checkbox,borderBottomLeftRadius:t.radii.full,borderTopLeftRadius:t.radii.full,borderBottomRightRadius:t.radii.full,borderTopRightRadius:t.radii.full,backgroundColor:l}}},Checkmark:{style:({$isFocusVisible:i,$checked:c})=>{const l=c&&!a?o.primary:o.fadedText40;return{outline:0,width:r.checkbox,height:r.checkbox,marginTop:t.spacing.twoXS,marginLeft:0,marginBottom:0,boxShadow:i&&c?`0 0 0 0.2rem ${R(o.primary,.5)}`:"",borderLeftWidth:r.borderWidth,borderRightWidth:r.borderWidth,borderTopWidth:r.borderWidth,borderBottomWidth:r.borderWidth,borderLeftColor:l,borderRightColor:l,borderTopColor:l,borderBottomColor:l}}},Label:{style:{lineHeight:t.lineHeights.small,paddingLeft:t.spacing.sm,position:"relative",color:u}}},children:C(w,{visibility:y(e.labelVisibility?.value),"data-testid":"stWidgetLabel",children:[n(L,{source:e.label,allowHTML:!1,isLabel:!0,largerLabel:!0}),e.help&&n(S,{color:u,children:n(W,{content:e.help,placement:B.TOP_RIGHT})})]})})})}function X(e,a){return e.getBoolValue(a)}function V(e){return e.default??null}function z(e){return e.value??null}function I(e,a,s,d){a.setBoolValue(e,s.value,{fromUi:s.fromUi},d)}const U=p.memo(M);export{U as default};
