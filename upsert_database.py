"""
Complete pipeline for processing PDF documents and uploading embeddings to Pinecone
using both simple and hierarchical chunking strategies.

Integrates with existing components:
- pinecone_client.py for Pinecone operations
- simple_chunk_strategy.py for basic chunking
- hierarchical_chunking_strategy.py for hierarchical chunking
"""

import os
import argparse
import hashlib
from datetime import datetime
from typing import List, Dict, Any, Optional
import time

# Import existing components
from pinecone_client import PineconeClient
from simple_chunk_strategy import process_pdf_simple
from hierarchical_chunking_strategy import process_pdf_hierarchical


class EmbeddingProcessor:
    """
    Handles OpenAI embedding generation and coordinates with Pinecone client
    """
    
    def __init__(self, index_name: str = None):
        """
        Initialize the embedding processor
        
        Args:
            index_name (str): Pinecone index name, defaults to environment variable
        """
        self.pinecone_client = PineconeClient()
        self.index_name = index_name or os.getenv("PINECONE_INDEX_NAME", "astrology-text")
        self.batch_size = 100
        
        # Verify Pinecone availability
        if not self.pinecone_client._pinecone_available:
            raise RuntimeError("Pinecone client is not available. Check API key configuration.")
        
        print(f"✅ EmbeddingProcessor initialized with index: {self.index_name}")
    
    def generate_vector_id(self, strategy: str, source_file: str, chunk_index: int, text_preview: str) -> str:
        """
        Generate unique vector ID using MD5 hash
        
        Args:
            strategy (str): Chunking strategy name
            source_file (str): Source file name
            chunk_index (int): Chunk index
            text_preview (str): First 50 characters of text
            
        Returns:
            str: Unique vector ID
        """
        content = f"{strategy}_{source_file}_{chunk_index}_{text_preview[:50]}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def process_chunks_batch(self, chunks: List[Dict[str, Any]], strategy: str, namespace: str) -> Dict[str, Any]:
        """
        Process chunks in batches and upload to Pinecone
        
        Args:
            chunks (List[Dict]): List of chunks to process
            strategy (str): Strategy name for metadata
            namespace (str): Pinecone namespace
            
        Returns:
            Dict: Processing statistics
        """
        if not chunks:
            return {"total_chunks": 0, "successful_uploads": 0, "failed_uploads": 0}
        
        print(f"🔄 Processing {len(chunks)} chunks for {strategy} strategy...")
        
        total_chunks = len(chunks)
        successful_uploads = 0
        failed_uploads = 0
        
        # Process in batches
        for i in range(0, len(chunks), self.batch_size):
            batch = chunks[i:i + self.batch_size]
            batch_num = i // self.batch_size + 1
            total_batches = (len(chunks) + self.batch_size - 1) // self.batch_size
            
            print(f"📦 Processing batch {batch_num}/{total_batches} ({len(batch)} chunks)...")
            
            try:
                # Prepare vectors for upload
                vectors_to_upload = []
                
                for chunk in batch:
                    # Generate unique vector ID
                    vector_id = self.generate_vector_id(
                        strategy=strategy,
                        source_file=chunk.get('source_file', 'unknown'),
                        chunk_index=chunk.get('chunk_index', 0),
                        text_preview=chunk.get('chunk_text', '')
                    )
                    
                    # Prepare metadata
                    metadata = {
                        "chunk_text": chunk.get('chunk_text', ''),
                        "chunk_index": chunk.get('chunk_index', 0),
                        "source_file": chunk.get('source_file', 'unknown'),
                        "strategy": strategy,
                        "created_at": datetime.now().isoformat()
                    }
                    
                    # Add hierarchical-specific metadata
                    if strategy == "hierarchical_chunking_strategy":
                        metadata.update({
                            "paragraph_context": chunk.get('paragraph_context', ''),
                            "paragraph_index": chunk.get('paragraph_index', 0)
                        })
                    
                    # Prepare vector data
                    vector_data = {
                        "id": vector_id,
                        "value": chunk.get('chunk_text', ''),  # Text to be embedded
                        "metadata": metadata
                    }
                    
                    vectors_to_upload.append(vector_data)
                
                # Upload batch to Pinecone
                self.pinecone_client.upsert_vectors(
                    index_name=self.index_name,
                    namespace=namespace,
                    embedded_data=vectors_to_upload
                )
                
                successful_uploads += len(batch)
                print(f"✅ Batch {batch_num} uploaded successfully")
                
                # Rate limiting
                time.sleep(1)
                
            except Exception as e:
                print(f"❌ Error processing batch {batch_num}: {str(e)}")
                failed_uploads += len(batch)
                continue
        
        return {
            "total_chunks": total_chunks,
            "successful_uploads": successful_uploads,
            "failed_uploads": failed_uploads,
            "success_rate": (successful_uploads / total_chunks * 100) if total_chunks > 0 else 0
        }
    
    def test_query(self, namespace: str, test_terms: List[str] = None) -> None:
        """
        Test queries using sample astrology-related terms
        
        Args:
            namespace (str): Namespace to query
            test_terms (List[str]): Test terms, defaults to astrology terms
        """
        if test_terms is None:
            test_terms = [
                "sun sign astrology",
                "moon phases",
                "planetary aspects",
                "birth chart interpretation",
                "zodiac houses"
            ]
        
        print(f"\n🔍 Testing queries in namespace '{namespace}'...")
        
        for term in test_terms:
            try:
                results = self.pinecone_client.query_vectors(
                    query=term,
                    index_name=self.index_name,
                    namespace=namespace,
                    top_k=3
                )
                
                print(f"📊 Query '{term}': Found {len(results)} results")
                if results:
                    top_result = results[0]
                    score = top_result.get('score', 0)
                    source = top_result.get('metadata', {}).get('source_file', 'unknown')
                    print(f"   Top result: {score:.3f} from {source}")
                
            except Exception as e:
                print(f"❌ Query '{term}' failed: {str(e)}")
        
        print()


def process_simple_chunking_pipeline(data_dir: str, processor: EmbeddingProcessor) -> Dict[str, Any]:
    """
    Process all PDFs using simple chunking strategy
    
    Args:
        data_dir (str): Directory containing PDF files
        processor (EmbeddingProcessor): Embedding processor instance
        
    Returns:
        Dict: Processing statistics
    """
    print("\n" + "="*60)
    print("🔄 SIMPLE CHUNKING PIPELINE")
    print("="*60)
    
    namespace = "simple_chunking_strategy"
    all_chunks = []
    
    # Find PDF files
    pdf_files = [f for f in os.listdir(data_dir) if f.lower().endswith('.pdf')]
    
    if not pdf_files:
        print(f"❌ No PDF files found in {data_dir}")
        return {"total_chunks": 0, "successful_uploads": 0, "failed_uploads": 0}
    
    print(f"📁 Found {len(pdf_files)} PDF files to process")
    
    # Process each PDF
    for pdf_file in pdf_files:
        pdf_path = os.path.join(data_dir, pdf_file)
        print(f"\n📄 Processing: {pdf_file}")
        
        try:
            # Use simple chunking strategy
            chunks = process_pdf_simple(pdf_path, chunk_size=250, chunk_overlap=50)
            all_chunks.extend(chunks)
            print(f"✅ Generated {len(chunks)} chunks from {pdf_file}")
            
        except Exception as e:
            print(f"❌ Failed to process {pdf_file}: {str(e)}")
            continue
    
    print(f"\n📊 Total chunks generated: {len(all_chunks)}")
    
    # Upload to Pinecone
    if all_chunks:
        stats = processor.process_chunks_batch(all_chunks, "simple_chunking_strategy", namespace)
        
        # Test queries
        processor.test_query(namespace)
        
        return stats
    else:
        return {"total_chunks": 0, "successful_uploads": 0, "failed_uploads": 0}


def process_hierarchical_chunking_pipeline(data_dir: str, processor: EmbeddingProcessor) -> Dict[str, Any]:
    """
    Process all PDFs using hierarchical chunking strategy
    
    Args:
        data_dir (str): Directory containing PDF files
        processor (EmbeddingProcessor): Embedding processor instance
        
    Returns:
        Dict: Processing statistics
    """
    print("\n" + "="*60)
    print("🔄 HIERARCHICAL CHUNKING PIPELINE")
    print("="*60)
    
    namespace = "hierarchical_chunking_strategy"
    all_chunks = []
    
    # Find PDF files
    pdf_files = [f for f in os.listdir(data_dir) if f.lower().endswith('.pdf')]
    
    if not pdf_files:
        print(f"❌ No PDF files found in {data_dir}")
        return {"total_chunks": 0, "successful_uploads": 0, "failed_uploads": 0}
    
    print(f"📁 Found {len(pdf_files)} PDF files to process")
    
    # Process each PDF
    for pdf_file in pdf_files:
        pdf_path = os.path.join(data_dir, pdf_file)
        print(f"\n📄 Processing: {pdf_file}")
        
        try:
            # Use hierarchical chunking strategy
            chunks = process_pdf_hierarchical(pdf_path)
            all_chunks.extend(chunks)
            print(f"✅ Generated {len(chunks)} chunks from {pdf_file}")
            
        except Exception as e:
            print(f"❌ Failed to process {pdf_file}: {str(e)}")
            continue
    
    print(f"\n📊 Total chunks generated: {len(all_chunks)}")
    
    # Upload to Pinecone
    if all_chunks:
        stats = processor.process_chunks_batch(all_chunks, "hierarchical_chunking_strategy", namespace)
        
        # Test queries
        processor.test_query(namespace)
        
        return stats
    else:
        return {"total_chunks": 0, "successful_uploads": 0, "failed_uploads": 0}


def main():
    """
    Main function with command-line interface
    """
    parser = argparse.ArgumentParser(
        description="Process PDF documents and upload embeddings to Pinecone using chunking strategies"
    )

    parser.add_argument(
        "--strategy",
        choices=["simple", "hierarchical", "both"],
        default="both",
        help="Chunking strategy to use (default: both)"
    )

    parser.add_argument(
        "--data-dir",
        type=str,
        default="./data",
        help="Directory containing PDF files (default: ./data)"
    )

    parser.add_argument(
        "--index-name",
        type=str,
        default=None,
        help="Pinecone index name (default: from environment variable)"
    )

    args = parser.parse_args()

    # Validate data directory
    if not os.path.exists(args.data_dir):
        print(f"❌ Data directory not found: {args.data_dir}")
        return

    print("🚀 PDF to Pinecone Upload Pipeline")
    print("="*50)
    print(f"📁 Data directory: {args.data_dir}")
    print(f"🎯 Strategy: {args.strategy}")
    print(f"🗂️  Index name: {args.index_name or os.getenv('PINECONE_INDEX_NAME', 'astrology-text')}")
    print()

    try:
        # Initialize processor
        processor = EmbeddingProcessor(index_name=args.index_name)

        # Track overall statistics
        total_stats = {
            "simple": {"total_chunks": 0, "successful_uploads": 0, "failed_uploads": 0},
            "hierarchical": {"total_chunks": 0, "successful_uploads": 0, "failed_uploads": 0}
        }

        start_time = time.time()

        # Execute pipelines based on strategy
        if args.strategy in ["simple", "both"]:
            simple_stats = process_simple_chunking_pipeline(args.data_dir, processor)
            total_stats["simple"] = simple_stats

        if args.strategy in ["hierarchical", "both"]:
            hierarchical_stats = process_hierarchical_chunking_pipeline(args.data_dir, processor)
            total_stats["hierarchical"] = hierarchical_stats

        # Display final statistics
        total_time = time.time() - start_time

        print("\n" + "="*60)
        print("📊 FINAL PROCESSING STATISTICS")
        print("="*60)

        for strategy_name, stats in total_stats.items():
            if stats["total_chunks"] > 0:
                print(f"\n{strategy_name.upper()} STRATEGY:")
                print(f"  📦 Total chunks: {stats['total_chunks']}")
                print(f"  ✅ Successful uploads: {stats['successful_uploads']}")
                print(f"  ❌ Failed uploads: {stats['failed_uploads']}")
                print(f"  📈 Success rate: {stats.get('success_rate', 0):.1f}%")

        # Calculate overall totals
        total_chunks = sum(stats["total_chunks"] for stats in total_stats.values())
        total_successful = sum(stats["successful_uploads"] for stats in total_stats.values())
        total_failed = sum(stats["failed_uploads"] for stats in total_stats.values())
        overall_success_rate = (total_successful / total_chunks * 100) if total_chunks > 0 else 0

        print(f"\nOVERALL TOTALS:")
        print(f"  📦 Total chunks processed: {total_chunks}")
        print(f"  ✅ Total successful uploads: {total_successful}")
        print(f"  ❌ Total failed uploads: {total_failed}")
        print(f"  📈 Overall success rate: {overall_success_rate:.1f}%")
        print(f"  ⏱️  Total processing time: {total_time:.2f} seconds")

        if total_successful > 0:
            print(f"\n🎉 Pipeline completed successfully!")
            print(f"📊 Embeddings are now available in Pinecone index '{processor.index_name}'")
        else:
            print(f"\n⚠️  Pipeline completed with no successful uploads")

    except Exception as e:
        print(f"❌ Pipeline failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
