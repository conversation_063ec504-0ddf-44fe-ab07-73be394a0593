"""
Test script for the upsert_database.py pipeline
This script tests the functionality without requiring actual API keys
"""

import os
import sys
from unittest.mock import Mock, patch
from upsert_database import EmbeddingProcessor, create_vector_id, PINECONE_INDEX_NAME


def test_vector_id_creation():
    """Test vector ID creation function"""
    print("Testing vector ID creation...")
    
    # Test simple chunk data
    simple_chunk = {
        'chunk_text': 'This is a test chunk about astrology houses.',
        'chunk_index': 0,
        'source_file': 'test.pdf'
    }
    
    simple_id = create_vector_id(simple_chunk, "simple")
    print(f"✅ Simple vector ID: {simple_id}")
    
    # Test hierarchical chunk data
    hierarchical_chunk = {
        'chunk_text': 'This is a test sentence.',
        'chunk_index': 0,
        'paragraph_context': 'This is a test sentence. It is part of a larger paragraph.',
        'paragraph_index': 0,
        'source_file': 'test.pdf'
    }
    
    hierarchical_id = create_vector_id(hierarchical_chunk, "hierarchical")
    print(f"✅ Hierarchical vector ID: {hierarchical_id}")
    
    # Verify IDs are different
    assert simple_id != hierarchical_id, "Vector IDs should be different for different strategies"
    print("✅ Vector IDs are unique")


def test_embedding_processor_initialization():
    """Test EmbeddingProcessor initialization without real API keys"""
    print("\nTesting EmbeddingProcessor initialization...")
    
    # Test without API keys (should handle gracefully)
    with patch.dict(os.environ, {}, clear=True):
        processor = EmbeddingProcessor()
        
        # Should initialize but with warnings
        print("✅ EmbeddingProcessor initialized without API keys (expected warnings)")
        
        # Test configuration
        assert PINECONE_INDEX_NAME == "astrology-text", f"Expected index name 'astrology-text', got '{PINECONE_INDEX_NAME}'"
        print(f"✅ Index name configured correctly: {PINECONE_INDEX_NAME}")


def test_chunking_strategy_imports():
    """Test that chunking strategies can be imported and used"""
    print("\nTesting chunking strategy imports...")
    
    try:
        from simple_chunk_strategy import load_pdf, extract_paragraphs, create_metadata
        print("✅ Simple chunking strategy functions imported successfully")
        
        from hierarchical_chunking_strategy import (
            load_pdf as h_load_pdf, 
            extract_paragraphs as h_extract_paragraphs,
            split_paragraph_to_sentences,
            create_hierarchical_chunks
        )
        print("✅ Hierarchical chunking strategy functions imported successfully")
        
        # Test function signatures exist
        assert callable(load_pdf), "load_pdf should be callable"
        assert callable(extract_paragraphs), "extract_paragraphs should be callable"
        assert callable(create_metadata), "create_metadata should be callable"
        assert callable(split_paragraph_to_sentences), "split_paragraph_to_sentences should be callable"
        assert callable(create_hierarchical_chunks), "create_hierarchical_chunks should be callable"
        
        print("✅ All required functions are callable")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    
    return True


def test_mock_pipeline():
    """Test pipeline with mocked dependencies"""
    print("\nTesting mock pipeline...")
    
    # Mock data
    mock_chunks = [
        {
            'chunk_text': 'The first house represents the self and personality.',
            'chunk_index': 0,
            'source_file': 'astrology_test.pdf'
        },
        {
            'chunk_text': 'The second house is associated with values and possessions.',
            'chunk_index': 1,
            'source_file': 'astrology_test.pdf'
        }
    ]
    
    mock_hierarchical_chunks = [
        {
            'chunk_text': 'The first house represents the self.',
            'chunk_index': 0,
            'paragraph_context': 'The first house represents the self and personality. It shows how others see you.',
            'paragraph_index': 0,
            'source_file': 'astrology_test.pdf'
        },
        {
            'chunk_text': 'It shows how others see you.',
            'chunk_index': 1,
            'paragraph_context': 'The first house represents the self and personality. It shows how others see you.',
            'paragraph_index': 0,
            'source_file': 'astrology_test.pdf'
        }
    ]
    
    # Test vector creation for simple chunks
    print("Testing simple chunk vector creation...")
    for chunk in mock_chunks:
        vector_id = create_vector_id(chunk, "simple")
        print(f"  Chunk {chunk['chunk_index']}: {vector_id}")
    
    # Test vector creation for hierarchical chunks
    print("Testing hierarchical chunk vector creation...")
    for chunk in mock_hierarchical_chunks:
        vector_id = create_vector_id(chunk, "hierarchical")
        print(f"  Chunk {chunk['chunk_index']}: {vector_id}")
    
    print("✅ Mock pipeline test completed")


def test_environment_configuration():
    """Test environment variable configuration"""
    print("\nTesting environment configuration...")
    
    # Test default values
    default_index = os.getenv("PINECONE_INDEX_NAME", "astrology-text")
    print(f"✅ Default index name: {default_index}")
    
    # Test with custom environment variable
    with patch.dict(os.environ, {"PINECONE_INDEX_NAME": "custom-index"}):
        custom_index = os.getenv("PINECONE_INDEX_NAME", "astrology-text")
        print(f"✅ Custom index name: {custom_index}")
        assert custom_index == "custom-index", "Environment variable override should work"
    
    print("✅ Environment configuration working correctly")


def main():
    """Run all tests"""
    print("🧪 Testing upsert_database.py pipeline")
    print("=" * 50)
    
    try:
        # Run tests
        test_vector_id_creation()
        test_embedding_processor_initialization()
        
        if test_chunking_strategy_imports():
            test_mock_pipeline()
        
        test_environment_configuration()
        
        print("\n" + "=" * 50)
        print("✅ All tests passed!")
        print("\n📋 Next steps to run the actual pipeline:")
        print("1. Set up your OpenAI API key: export OPENAI_API_KEY='your-key'")
        print("2. Set up your Pinecone API key: export PINECONE_API_KEY='your-key'")
        print("3. Run the pipeline: python upsert_database.py --strategy both")
        print("4. Or run with comparison: python upsert_database.py --strategy both --compare")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
