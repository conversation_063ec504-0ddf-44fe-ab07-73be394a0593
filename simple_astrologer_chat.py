#!/usr/bin/env python3
"""
Simple Streamlit Chat Interface for AI Astrologer
Clean, single-page ChatGPT-like interface for astrology consultations.
"""

import streamlit as st
import asyncio
from datetime import datetime
from astrologer_prototype import AstrologerPrototype

# Configure Streamlit page
st.set_page_config(
    page_title="🔮 AI Astrologer Chat",
    page_icon="🌟",
    layout="centered",
    initial_sidebar_state="collapsed"
)

# Simple CSS for chat styling
st.markdown("""
<style>
    .user-message {
        background-color: #E3F2FD;
        padding: 10px;
        border-radius: 10px;
        margin: 10px 0;
        border-left: 4px solid #2196F3;
    }
    .astrologer-message {
        background-color: #F3E5F5;
        padding: 10px;
        border-radius: 10px;
        margin: 10px 0;
        border-left: 4px solid #9C27B0;
    }
    .chat-container {
        max-height: 600px;
        overflow-y: auto;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 10px;
        margin-bottom: 20px;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'astrologer' not in st.session_state:
    st.session_state.astrologer = None
if 'conversation' not in st.session_state:
    st.session_state.conversation = []
if 'input_key' not in st.session_state:
    st.session_state.input_key = 0

def initialize_astrologer():
    """Initialize the astrologer system."""
    try:
        st.session_state.astrologer = AstrologerPrototype(similarity_threshold=0.7)
        return True
    except Exception as e:
        st.error(f"Failed to initialize astrologer: {e}")
        return False

async def get_response(question):
    """Get response from astrologer."""
    if st.session_state.astrologer is None:
        return None
    
    try:
        result = await st.session_state.astrologer.get_astrology_response(question, enable_rag=True)
        return result['response']
    except Exception as e:
        st.error(f"Error getting response: {e}")
        return None

def add_message(role, content):
    """Add a message to the conversation."""
    st.session_state.conversation.append({
        "role": role,
        "content": content,
        "timestamp": datetime.now().strftime("%H:%M")
    })

def display_conversation():
    """Display the conversation history."""
    if not st.session_state.conversation:
        st.info("👋 Welcome! Ask me any astrology question to start our conversation.")
        return
    
    # Create scrollable chat container
    with st.container():
        for message in st.session_state.conversation:
            if message["role"] == "user":
                st.markdown(f"""
                <div class="user-message">
                    <strong>🤔 You ({message['timestamp']}):</strong><br>
                    {message['content']}
                </div>
                """, unsafe_allow_html=True)
            else:
                st.markdown(f"""
                <div class="astrologer-message">
                    <strong>🔮 AI Astrologer ({message['timestamp']}):</strong><br>
                    {message['content']}
                </div>
                """, unsafe_allow_html=True)

def main():
    """Main Streamlit application."""
    
    # Header
    st.title("🔮 AI Astrologer Chat")
    st.markdown("*Your personal astrological consultant powered by AI*")
    
    # Initialize system if needed
    if st.session_state.astrologer is None:
        with st.spinner("🌟 Initializing astrologer system..."):
            if initialize_astrologer():
                st.success("✨ Astrologer ready! Ask your first question below.")
            else:
                st.error("❌ Failed to initialize. Please refresh the page.")
                return
    
    # Display conversation
    display_conversation()
    
    # Input area
    st.markdown("---")
    
    # Use form for better UX
    with st.form(key=f"chat_form_{st.session_state.input_key}", clear_on_submit=True):
        col1, col2 = st.columns([4, 1])
        
        with col1:
            user_input = st.text_input(
                "Ask your astrology question:",
                placeholder="e.g., What does Mars in the 7th house mean?",
                key=f"user_input_{st.session_state.input_key}",
                label_visibility="collapsed"
            )
        
        with col2:
            submit_button = st.form_submit_button("Send 🚀", type="primary", use_container_width=True)
    
    # Process input
    if submit_button and user_input:
        # Add user message
        add_message("user", user_input)
        
        # Show typing indicator
        with st.spinner("🌟 Consulting the stars..."):
            # Get astrologer response
            response = asyncio.run(get_response(user_input))
            
            if response:
                # Add astrologer response
                add_message("astrologer", response)
                
                # Increment input key to refresh form
                st.session_state.input_key += 1
                
                # Rerun to show new messages
                st.rerun()
            else:
                st.error("Sorry, I couldn't generate a response. Please try again.")
    
    # Sidebar with minimal controls
    with st.sidebar:
        st.header("⚙️ Chat Controls")
        
        if st.button("🗑️ Clear Conversation"):
            st.session_state.conversation = []
            st.session_state.input_key += 1
            st.rerun()
        
        st.metric("Messages", len(st.session_state.conversation))
        
        if st.session_state.astrologer:
            st.success("✅ System Ready")
        else:
            st.warning("⚠️ System Not Ready")
        
        # Sample questions
        st.markdown("---")
        st.subheader("💡 Sample Questions")
        sample_questions = [
            "What does my Sun sign reveal?",
            "How do Mercury retrogrades affect me?",
            "What is a Saturn return?",
            "How do lunar phases impact emotions?",
            "What does Venus in my chart mean?"
        ]
        
        for question in sample_questions:
            if st.button(question, key=f"sample_{question[:10]}"):
                # Add the sample question as if user typed it
                add_message("user", question)
                
                with st.spinner("🌟 Consulting the stars..."):
                    response = asyncio.run(get_response(question))
                    if response:
                        add_message("astrologer", response)
                        st.session_state.input_key += 1
                        st.rerun()

if __name__ == "__main__":
    main()
