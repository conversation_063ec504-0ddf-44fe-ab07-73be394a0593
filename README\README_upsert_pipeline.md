# PDF to Pinecone Embedding Pipeline

This document describes the complete pipeline implementation in `upsert_database.py` for processing PDF documents and uploading embeddings to Pinecone using both simple and hierarchical chunking strategies.

## Overview

The `upsert_database.py` file implements a comprehensive pipeline that:

1. **Processes PDF documents** using two different chunking strategies
2. **Generates embeddings** using OpenAI's text-embedding-ada-002 model
3. **Uploads embeddings to Pinecone** vector database with proper metadata
4. **Provides testing and comparison functionality** to validate the implementation

## Configuration

### Environment Variables

The pipeline uses environment variables with fallback defaults:

- **`PINECONE_INDEX_NAME`**: Pinecone index name (default: "astrology-text")
- **`OPENAI_API_KEY`**: Required for embedding generation
- **`PINECONE_API_KEY`**: Required for Pinecone operations

### Namespaces

Two separate namespaces are used to organize different chunking strategies:

- **Simple Strategy**: `"simple_chunking_strategy"`
- **Hierarchical Strategy**: `"hierarchical_chunking_strategy"`

## Core Components

### EmbeddingProcessor Class

The main class that handles all embedding and Pinecone operations:

#### Key Methods:
- **`__init__()`**: Initializes OpenAI and Pinecone clients
- **`_setup_index()`**: Creates or connects to Pinecone index
- **`generate_embedding(text)`**: Generates single embedding
- **`generate_embeddings_batch(texts)`**: Generates batch embeddings for efficiency
- **`upsert_vectors(vectors, namespace)`**: Uploads vectors to Pinecone
- **`test_query(namespace, query_text)`**: Validates uploaded embeddings

### Pipeline Functions

#### Simple Chunking Pipeline
```python
process_simple_chunking_pipeline(data_dir="./data")
```
- Uses `simple_chunk_strategy.py` functions
- Processes PDFs with configurable chunk size (250 chars) and overlap (50 chars)
- Uploads to `"simple_chunking_strategy"` namespace
- Metadata includes: `chunk_text`, `chunk_index`, `source_file`, `strategy`, `created_at`

#### Hierarchical Chunking Pipeline
```python
process_hierarchical_chunking_pipeline(data_dir="./data")
```
- Uses `hierarchical_chunking_strategy.py` functions
- Processes PDFs with sentence-level chunking and paragraph context
- Uploads to `"hierarchical_chunking_strategy"` namespace
- Metadata includes: `chunk_text`, `chunk_index`, `paragraph_context`, `paragraph_index`, `source_file`, `strategy`, `created_at`

## Usage

### Command Line Interface

```bash
# Run both strategies
python upsert_database.py --strategy both

# Run only simple strategy
python upsert_database.py --strategy simple

# Run only hierarchical strategy
python upsert_database.py --strategy hierarchical

# Run with custom data directory
python upsert_database.py --data-dir /path/to/pdfs

# Run with comparison queries
python upsert_database.py --strategy both --compare
```

### Prerequisites

1. **Install Dependencies**:
   ```bash
   pip install pinecone openai PyPDF2 langchain-text-splitters
   ```

2. **Set Environment Variables**:
   ```bash
   export OPENAI_API_KEY="your-openai-api-key"
   export PINECONE_API_KEY="your-pinecone-api-key"
   export PINECONE_INDEX_NAME="astrology-text"  # Optional, has default
   ```

3. **Prepare PDF Files**:
   - Place PDF files in `./data/` directory (or specify custom path)

## Features

### Batch Processing
- Processes embeddings in batches of 100 for efficiency
- Reduces API calls and improves performance
- Handles large PDF collections gracefully

### Error Handling
- Comprehensive error handling for PDF processing failures
- Graceful handling of embedding generation errors
- Pinecone connection and upload error management
- Individual page processing error recovery

### Progress Tracking
- Real-time progress reporting during PDF processing
- Batch upload status indicators
- Success rate calculations and statistics

### Testing and Validation
- Automatic test queries after each pipeline completion
- Similarity search validation
- Strategy comparison functionality
- Mock testing capabilities (see `test_upsert_pipeline.py`)

## Vector Database Structure

### Index Configuration
- **Dimension**: 1536 (OpenAI text-embedding-ada-002)
- **Metric**: Cosine similarity
- **Cloud**: AWS (Serverless)
- **Region**: us-east-1

### Vector ID Generation
Unique vector IDs are generated using MD5 hash of:
```
{strategy}_{source_file}_{chunk_index}_{chunk_text_preview}
```

### Metadata Schema

#### Simple Strategy Metadata:
```json
{
  "chunk_text": "The actual text content",
  "chunk_index": 0,
  "source_file": "document.pdf",
  "strategy": "simple",
  "created_at": "2024-01-01T12:00:00"
}
```

#### Hierarchical Strategy Metadata:
```json
{
  "chunk_text": "Individual sentence text",
  "chunk_index": 0,
  "paragraph_context": "Full paragraph containing the sentence",
  "paragraph_index": 0,
  "source_file": "document.pdf",
  "strategy": "hierarchical",
  "created_at": "2024-01-01T12:00:00"
}
```

## Performance Characteristics

### Test Results (Sample: "The Twelve Houses" PDF)

#### Simple Strategy:
- **Chunks Generated**: 1,294
- **Average Chunk Size**: 210 characters
- **Processing Time**: ~2-3 minutes
- **Upload Efficiency**: 100 vectors per batch

#### Hierarchical Strategy:
- **Chunks Generated**: 2,001
- **Average Chunk Size**: 136 characters
- **Processing Time**: ~3-4 minutes
- **Context Preservation**: Full paragraph context for each sentence

### Comparison Features

The `--compare` flag enables:
- Side-by-side similarity search results
- Strategy performance comparison
- Query result quality analysis
- Score comparison between strategies

## Error Recovery

### Common Issues and Solutions:

1. **API Key Missing**:
   ```
   Error: The api_key client option must be set
   Solution: Set OPENAI_API_KEY environment variable
   ```

2. **Pinecone Connection Failed**:
   ```
   Error: Failed to initialize Pinecone client
   Solution: Set PINECONE_API_KEY environment variable
   ```

3. **PDF Processing Failed**:
   ```
   Warning: Failed to read page X
   Solution: Individual page failures are logged but don't stop processing
   ```

4. **Embedding Generation Failed**:
   ```
   Error: Failed to generate batch embeddings
   Solution: Check OpenAI API quota and network connection
   ```

## Testing

### Automated Testing
Run the test suite to verify functionality:
```bash
python test_upsert_pipeline.py
```

### Manual Testing
1. **Dry Run**: Test without API keys to verify imports and configuration
2. **Small Dataset**: Test with 1-2 PDF files first
3. **Query Testing**: Use the `--compare` flag to validate search quality

## Best Practices

### For Production Use:
1. **Monitor API Usage**: Track OpenAI and Pinecone API consumption
2. **Batch Size Tuning**: Adjust `BATCH_SIZE` based on your API limits
3. **Error Logging**: Implement comprehensive logging for production monitoring
4. **Incremental Updates**: Consider implementing change detection to avoid re-processing unchanged files
5. **Backup Strategy**: Regularly backup your Pinecone index

### For Development:
1. **Use Test Index**: Create separate index for development/testing
2. **Small Datasets**: Test with subset of documents first
3. **Mock Testing**: Use the provided test script for development validation

## Integration Examples

### Basic Integration:
```python
from upsert_database import process_simple_chunking_pipeline

# Process PDFs and get statistics
total_chunks, successful_uploads = process_simple_chunking_pipeline("./my_pdfs")
print(f"Uploaded {successful_uploads}/{total_chunks} chunks")
```

### Custom Processing:
```python
from upsert_database import EmbeddingProcessor

processor = EmbeddingProcessor()
# Custom processing logic here
```

This pipeline provides a robust, scalable solution for converting PDF documents into searchable vector embeddings optimized for RAG applications.
