"""
占星學知識庫RAG系統主程式
功能：
1. 處理PDF文件並生成chunks
2. 上傳到Pinecone向量資料庫
3. 比較RAG vs 非RAG的回答效果
"""

import os
import asyncio
import time
from typing import List, Dict, Optional

from chunk_processor import process_all_pdfs
from pinecone_client import PineconeClient
from gpt4o_client import GPT4oClient
from config import config


class AstrologyRAGSystem:
    """占星學RAG系統"""
    
    def __init__(self):
        """初始化系統組件"""
        print("🚀 初始化占星學RAG系統...")
        
        # 初始化客戶端
        self.pinecone_client = PineconeClient()
        self.gpt4o_client = GPT4oClient()
        
        # 系統配置
        self.data_dir = "./data"
        self.index_name = config.PINECONE_INDEX_NAME or "astrology-text"
        self.namespace = config.PINECONE_NAMESPACE or "astrology-text"
        
        print(f"📊 配置資訊:")
        print(f"   - 資料目錄: {self.data_dir}")
        print(f"   - Pinecone索引: {self.index_name}")
        print(f"   - 命名空間: {self.namespace}")
        print(f"   - Config中的PINECONE_NAMESPACE: {config.PINECONE_NAMESPACE}")
        print()

    async def check_existing_vectors(self) -> bool:
        """
        檢查Pinecone中是否已存在向量資料

        Returns:
            bool: True表示已存在資料，可跳過上傳；False表示需要上傳
        """
        try:
            if not self.pinecone_client._pinecone_available:
                print("⚠️  Pinecone不可用，無法檢查現有向量")
                return False

            # 嘗試查詢一個簡單的測試問題來檢查是否有資料
            test_query = "astrology"
            results = self.pinecone_client.search_rag_context(
                user_query=test_query,
                index_name=self.index_name,
                namespace=self.namespace,
                top_k=1
            )

            if results and len(results) > 0:
                print(f"✅ 檢測到 {len(results)} 個現有向量，相似度: {results[0].get('score', 0):.3f}")
                return True
            else:
                print("❌ 未檢測到現有向量資料")
                return False

        except Exception as e:
            print(f"⚠️  檢查向量時發生錯誤: {str(e)}")
            print("   將繼續執行上傳流程...")
            return False

    def upload_knowledge_base(self, force_reprocess: bool = False) -> bool:
        """
        上傳知識庫到向量資料庫
        
        Args:
            force_reprocess (bool): 是否強制重新處理PDF
            
        Returns:
            bool: 是否成功
        """
        try:
            print("📚 === 第一階段：PDF處理 ===")
            
            # 處理PDF文件
            chunks = process_all_pdfs(
                data_dir=self.data_dir,
                chunk_size=250,
                chunk_overlap=50
            )
            
            if not chunks:
                print("❌ 沒有找到可處理的chunks")
                return False
            
            print(f"\n🔄 === 第二階段：向量化上傳 ===")
            print(f"準備上傳 {len(chunks)} 個chunks到Pinecone...")
            
            # 準備上傳資料
            upload_data = []
            for chunk in chunks:
                upload_item = {
                    "id": chunk["metadata"]["chunk_id"],
                    "value": chunk["text"],  # 文本內容，將被自動embedding
                    "metadata": {
                        **chunk["metadata"],
                        "text": chunk["text"]  # 在metadata中也保存文本以便檢索
                    }
                }
                upload_data.append(upload_item)
            
            # 批量上傳
            batch_size = 50
            total_batches = (len(upload_data) + batch_size - 1) // batch_size
            
            for i in range(0, len(upload_data), batch_size):
                batch = upload_data[i:i + batch_size]
                batch_num = i // batch_size + 1
                
                print(f"上傳批次 {batch_num}/{total_batches} ({len(batch)} 個chunks)...")
                
                self.pinecone_client.upsert_vectors(
                    index_name=self.index_name,
                    namespace=self.namespace,
                    embedded_data=batch
                )
                
                time.sleep(1)  # 避免API限制
            
            print(f"✅ 知識庫上傳完成！總計 {len(chunks)} 個chunks")
            return True
            
        except Exception as e:
            print(f"❌ 知識庫上傳失敗: {str(e)}")
            return False
    
    async def test_rag_vs_non_rag(self, question: str) -> Dict:
        """
        測試RAG vs 非RAG的回答比較
        
        Args:
            question (str): 測試問題
            
        Returns:
            Dict: 比較結果
        """
        print(f"\n🤔 測試問題: {question}")
        print("=" * 60)
        
        # 系統提示詞
        system_prompt = """你是一位專業的占星學專家，擅長解讀星座、行星、宮位等占星學概念。
請用專業但易懂的方式回答用戶的占星學問題，提供準確和有用的資訊。"""
        
        result = {
            "question": question,
            "non_rag_answer": "",
            "rag_answer": "",
            "rag_sources": [],
            "rag_scores": [],
            "response_time": {"non_rag": 0, "rag": 0},
            "analysis": ""
        }
        
        try:
            # 1. 非RAG回答
            print("🤖 生成非RAG回答...")
            start_time = time.time()
            
            non_rag_answer = await self.gpt4o_client.generate_response(
                system_prompt=system_prompt,
                user_input=question,
                rag_context=None,
                temperature=0.7
            )
            
            result["response_time"]["non_rag"] = time.time() - start_time
            result["non_rag_answer"] = non_rag_answer
            
            print(f"⏱️  非RAG回答時間: {result['response_time']['non_rag']:.2f}秒")
            print(f"📝 非RAG回答:\n{non_rag_answer}\n")
            
            # 2. RAG回答
            print("🔍 搜尋相關知識...")
            rag_context = self.pinecone_client.search_rag_context(
                user_query=question,
                index_name=self.index_name,
                namespace=self.namespace,
                top_k=5
            )
            
            if rag_context:
                print(f"找到 {len(rag_context)} 個相關資料:")
                for i, ctx in enumerate(rag_context, 1):
                    score = ctx.get("score", 0)
                    source = ctx.get("metadata", {}).get("source", "未知")
                    print(f"  {i}. 來源: {source}, 相似度: {score:.3f}")
                    result["rag_scores"].append(score)
                    if source not in result["rag_sources"]:
                        result["rag_sources"].append(source)
            else:
                print("⚠️  未找到相關資料，將使用空的RAG上下文")
            
            print("\n🤖 生成RAG增強回答...")
            start_time = time.time()
            
            rag_answer = await self.gpt4o_client.generate_response(
                system_prompt=system_prompt,
                user_input=question,
                rag_context=rag_context,
                temperature=0.7
            )
            
            result["response_time"]["rag"] = time.time() - start_time
            result["rag_answer"] = rag_answer
            
            print(f"⏱️  RAG回答時間: {result['response_time']['rag']:.2f}秒")
            print(f"📝 RAG回答:\n{rag_answer}\n")
            
            # 3. 簡單分析
            non_rag_len = len(non_rag_answer)
            rag_len = len(rag_answer)
            
            analysis = f"""
📊 回答比較分析:
• 非RAG回答長度: {non_rag_len} 字符
• RAG回答長度: {rag_len} 字符
• 相關資料來源: {len(result['rag_sources'])} 個
• 平均相似度: {(sum(result['rag_scores'])/len(result['rag_scores']) if result['rag_scores'] else 0.000):.3f}
• 回答時間差異: RAG比非RAG多 {result['response_time']['rag'] - result['response_time']['non_rag']:.2f}秒
            """.strip()
            
            result["analysis"] = analysis
            print(analysis)
            
        except Exception as e:
            print(f"❌ 測試過程發生錯誤: {str(e)}")
            result["error"] = str(e)
        
        return result
    
    async def run_comparison_tests(self) -> List[Dict]:
        """
        執行完整的比較測試
        
        Returns:
            List[Dict]: 所有測試結果
        """
        # 測試問題集
        test_questions = [
            "一個金星在射手的人會是什麼樣的人呢？",
            "月亮處女與月亮射手的人分別是什麼樣的人呢？"
        ]
        
        print("🧪 === 開始RAG vs 非RAG比較測試 ===")
        print(f"測試問題數量: {len(test_questions)}")
        
        results = []
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n🔬 測試 {i}/{len(test_questions)}")
            result = await self.test_rag_vs_non_rag(question)
            results.append(result)
            
            # 測試間隔
            if i < len(test_questions):
                print("⏳ 等待3秒後進行下一個測試...")
                await asyncio.sleep(3)
        
        return results
    
    def generate_final_report(self, results: List[Dict]) -> None:
        """
        生成最終測試報告
        
        Args:
            results (List[Dict]): 測試結果列表
        """
        print("\n" + "=" * 80)
        print("📋 === 最終測試報告 ===")
        print("=" * 80)
        
        for i, result in enumerate(results, 1):
            print(f"\n🔍 測試 {i}: {result['question']}")
            print("-" * 60)
            
            if "error" in result:
                print(f"❌ 測試失敗: {result['error']}")
                continue
            
            print(f"📚 RAG資料來源: {', '.join(result['rag_sources']) if result['rag_sources'] else '無'}")
            print(f"⏱️  回答時間: 非RAG {result['response_time']['non_rag']:.2f}s, RAG {result['response_time']['rag']:.2f}s")
            print(f"📊 相似度分數: {result['rag_scores']}")
            print(result['analysis'])
        
        # 總體統計
        successful_tests = [r for r in results if "error" not in r]
        if successful_tests:
            avg_non_rag_time = sum(r['response_time']['non_rag'] for r in successful_tests) / len(successful_tests)
            avg_rag_time = sum(r['response_time']['rag'] for r in successful_tests) / len(successful_tests)
            
            print(f"\n📈 總體統計:")
            print(f"   - 成功測試: {len(successful_tests)}/{len(results)}")
            print(f"   - 平均非RAG回答時間: {avg_non_rag_time:.2f}秒")
            print(f"   - 平均RAG回答時間: {avg_rag_time:.2f}秒")
            print(f"   - RAG平均增加時間: {avg_rag_time - avg_non_rag_time:.2f}秒")


async def main():
    """主函數"""
    print("🌟 歡迎使用占星學RAG系統！")
    print("=" * 50)
    
    # 初始化系統
    system = AstrologyRAGSystem()
    
    try:
        # 檢查是否需要上傳知識庫
        skip_upload = await system.check_existing_vectors()

        if skip_upload:
            print("📚 檢測到現有知識庫，跳過上傳階段...")
            print(f"   - 索引: {system.index_name}")
            print(f"   - 命名空間: {system.namespace}")
            print("   - 直接使用已存在的向量資料")
        else:
            print("📤 開始上傳知識庫...")
            success = system.upload_knowledge_base()

            if not success:
                print("❌ 知識庫上傳失敗，無法繼續測試")
                return

        # 執行比較測試
        print("\n🔬 開始執行比較測試...")
        results = await system.run_comparison_tests()

        # 生成報告
        system.generate_final_report(results)

        print("\n✅ 所有測試完成！")
        
    except KeyboardInterrupt:
        print("\n⚠️  用戶中斷執行")
    except Exception as e:
        print(f"\n❌ 系統錯誤: {str(e)}")


if __name__ == "__main__":
    # 執行主程式
    asyncio.run(main())
