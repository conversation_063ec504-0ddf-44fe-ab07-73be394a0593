import{s,k as r,r as i,j as n}from"./index.CbQtRkVt.js";import{P as m,R as c}from"./RenderInPortalIfExists.D6a0mMll.js";const p=""+new URL("../media/balloon-0.Czj7AKwE.png",import.meta.url).href,f=""+new URL("../media/balloon-1.CNvFFrND.png",import.meta.url).href,d=""+new URL("../media/balloon-2.DTvC6B1t.png",import.meta.url).href,B=""+new URL("../media/balloon-3.CgSk4tbL.png",import.meta.url).href,L=""+new URL("../media/balloon-4.mbtFrzxf.png",import.meta.url).href,h=""+new URL("../media/balloon-5.CSwkUfRA.png",import.meta.url).href,t=300,a=121,e=20,g=80,u=1e3,x=r("from{transform:translateY(calc(100vh + ",t,"px));}to{transform:translateY(0);}"),_=s("img",{target:"eosrfo90"})(({theme:o})=>({position:"fixed",top:`${-t}px`,marginLeft:`${-a/2}px`,zIndex:o.zIndices.balloons,left:`${Math.random()*(g-e)+e}vw`,animationDelay:`${Math.random()*u}ms`,height:`${t}px`,width:`${a}px`,pointerEvents:"none",animationDuration:"750ms",animationName:x,animationTimingFunction:"ease-in",animationDirection:"normal",animationIterationCount:1,opacity:1})),A=30,l=[p,f,d,B,L,h],I=l.length,M=({particleType:o})=>n(_,{src:l[o]}),w=({scriptRunId:o})=>n(c,{children:n(m,{className:"stBalloons","data-testid":"stBalloons",scriptRunId:o,numParticleTypes:I,numParticles:A,ParticleComponent:M})}),U=i.memo(w);export{A as NUM_BALLOONS,U as default};
