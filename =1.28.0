Collecting streamlit
  Downloading streamlit-1.47.1-py3-none-any.whl.metadata (9.0 kB)
Collecting altair<6,>=4.0 (from streamlit)
  Downloading altair-5.5.0-py3-none-any.whl.metadata (11 kB)
Collecting blinker<2,>=1.5.0 (from streamlit)
  Downloading blinker-1.9.0-py3-none-any.whl.metadata (1.6 kB)
Collecting cachetools<7,>=4.0 (from streamlit)
  Downloading cachetools-6.1.0-py3-none-any.whl.metadata (5.4 kB)
Collecting click<9,>=7.0 (from streamlit)
  Downloading click-8.2.1-py3-none-any.whl.metadata (2.5 kB)
Collecting numpy<3,>=1.23 (from streamlit)
  Downloading numpy-2.3.2-cp312-cp312-win_amd64.whl.metadata (60 kB)
Requirement already satisfied: packaging<26,>=20 in c:\users\<USER>\documents\augment-projects\lens_astrology\.venv\lib\site-packages (from streamlit) (24.2)
Collecting pandas<3,>=1.4.0 (from streamlit)
  Downloading pandas-2.3.1-cp312-cp312-win_amd64.whl.metadata (19 kB)
Collecting pillow<12,>=7.1.0 (from streamlit)
  Downloading pillow-11.3.0-cp312-cp312-win_amd64.whl.metadata (9.2 kB)
Requirement already satisfied: protobuf<7,>=3.20 in c:\users\<USER>\documents\augment-projects\lens_astrology\.venv\lib\site-packages (from streamlit) (5.29.5)
Collecting pyarrow>=7.0 (from streamlit)
  Downloading pyarrow-21.0.0-cp312-cp312-win_amd64.whl.metadata (3.4 kB)
Requirement already satisfied: requests<3,>=2.27 in c:\users\<USER>\documents\augment-projects\lens_astrology\.venv\lib\site-packages (from streamlit) (2.32.4)
Requirement already satisfied: tenacity<10,>=8.1.0 in c:\users\<USER>\documents\augment-projects\lens_astrology\.venv\lib\site-packages (from streamlit) (9.1.2)
Collecting toml<2,>=0.10.1 (from streamlit)
  Downloading toml-0.10.2-py2.py3-none-any.whl.metadata (7.1 kB)
Requirement already satisfied: typing-extensions<5,>=4.4.0 in c:\users\<USER>\documents\augment-projects\lens_astrology\.venv\lib\site-packages (from streamlit) (4.14.1)
Collecting watchdog<7,>=2.1.5 (from streamlit)
  Downloading watchdog-6.0.0-py3-none-win_amd64.whl.metadata (44 kB)
Collecting gitpython!=3.1.19,<4,>=3.0.7 (from streamlit)
  Downloading gitpython-3.1.45-py3-none-any.whl.metadata (13 kB)
Collecting pydeck<1,>=0.8.0b4 (from streamlit)
  Downloading pydeck-0.9.1-py2.py3-none-any.whl.metadata (4.1 kB)
Collecting tornado!=6.5.0,<7,>=6.0.3 (from streamlit)
  Downloading tornado-6.5.1-cp39-abi3-win_amd64.whl.metadata (2.9 kB)
Collecting jinja2 (from altair<6,>=4.0->streamlit)
  Downloading jinja2-3.1.6-py3-none-any.whl.metadata (2.9 kB)
Collecting jsonschema>=3.0 (from altair<6,>=4.0->streamlit)
  Downloading jsonschema-4.25.0-py3-none-any.whl.metadata (7.7 kB)
Collecting narwhals>=1.14.2 (from altair<6,>=4.0->streamlit)
  Downloading narwhals-2.0.1-py3-none-any.whl.metadata (11 kB)
Requirement already satisfied: colorama in c:\users\<USER>\documents\augment-projects\lens_astrology\.venv\lib\site-packages (from click<9,>=7.0->streamlit) (0.4.6)
Collecting gitdb<5,>=4.0.1 (from gitpython!=3.1.19,<4,>=3.0.7->streamlit)
  Downloading gitdb-4.0.12-py3-none-any.whl.metadata (1.2 kB)
Requirement already satisfied: python-dateutil>=2.8.2 in c:\users\<USER>\documents\augment-projects\lens_astrology\.venv\lib\site-packages (from pandas<3,>=1.4.0->streamlit) (2.9.0.post0)
Collecting pytz>=2020.1 (from pandas<3,>=1.4.0->streamlit)
  Downloading pytz-2025.2-py2.py3-none-any.whl.metadata (22 kB)
Collecting tzdata>=2022.7 (from pandas<3,>=1.4.0->streamlit)
  Downloading tzdata-2025.2-py2.py3-none-any.whl.metadata (1.4 kB)
Requirement already satisfied: charset_normalizer<4,>=2 in c:\users\<USER>\documents\augment-projects\lens_astrology\.venv\lib\site-packages (from requests<3,>=2.27->streamlit) (3.4.2)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\documents\augment-projects\lens_astrology\.venv\lib\site-packages (from requests<3,>=2.27->streamlit) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\documents\augment-projects\lens_astrology\.venv\lib\site-packages (from requests<3,>=2.27->streamlit) (2.5.0)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\documents\augment-projects\lens_astrology\.venv\lib\site-packages (from requests<3,>=2.27->streamlit) (2025.7.14)
Collecting smmap<6,>=3.0.1 (from gitdb<5,>=4.0.1->gitpython!=3.1.19,<4,>=3.0.7->streamlit)
  Downloading smmap-5.0.2-py3-none-any.whl.metadata (4.3 kB)
Collecting MarkupSafe>=2.0 (from jinja2->altair<6,>=4.0->streamlit)
  Downloading MarkupSafe-3.0.2-cp312-cp312-win_amd64.whl.metadata (4.1 kB)
Collecting attrs>=22.2.0 (from jsonschema>=3.0->altair<6,>=4.0->streamlit)
  Downloading attrs-25.3.0-py3-none-any.whl.metadata (10 kB)
Collecting jsonschema-specifications>=2023.03.6 (from jsonschema>=3.0->altair<6,>=4.0->streamlit)
  Downloading jsonschema_specifications-2025.4.1-py3-none-any.whl.metadata (2.9 kB)
Collecting referencing>=0.28.4 (from jsonschema>=3.0->altair<6,>=4.0->streamlit)
  Downloading referencing-0.36.2-py3-none-any.whl.metadata (2.8 kB)
Collecting rpds-py>=0.7.1 (from jsonschema>=3.0->altair<6,>=4.0->streamlit)
  Downloading rpds_py-0.26.0-cp312-cp312-win_amd64.whl.metadata (4.3 kB)
Requirement already satisfied: six>=1.5 in c:\users\<USER>\documents\augment-projects\lens_astrology\.venv\lib\site-packages (from python-dateutil>=2.8.2->pandas<3,>=1.4.0->streamlit) (1.17.0)
Downloading streamlit-1.47.1-py3-none-any.whl (9.9 MB)
   ---------------------------------------- 9.9/9.9 MB 3.3 MB/s eta 0:00:00
Downloading altair-5.5.0-py3-none-any.whl (731 kB)
   ---------------------------------------- 731.2/731.2 kB 7.4 MB/s eta 0:00:00
Downloading blinker-1.9.0-py3-none-any.whl (8.5 kB)
Downloading cachetools-6.1.0-py3-none-any.whl (11 kB)
Downloading click-8.2.1-py3-none-any.whl (102 kB)
Downloading gitpython-3.1.45-py3-none-any.whl (208 kB)
Downloading numpy-2.3.2-cp312-cp312-win_amd64.whl (12.8 MB)
   ---------------------------------------- 12.8/12.8 MB 9.4 MB/s eta 0:00:00
Downloading pandas-2.3.1-cp312-cp312-win_amd64.whl (11.0 MB)
   ---------------------------------------- 11.0/11.0 MB 9.7 MB/s eta 0:00:00
Downloading pillow-11.3.0-cp312-cp312-win_amd64.whl (7.0 MB)
   ---------------------------------------- 7.0/7.0 MB 10.0 MB/s eta 0:00:00
Downloading pyarrow-21.0.0-cp312-cp312-win_amd64.whl (26.2 MB)
   ---------------------------------------- 26.2/26.2 MB 10.9 MB/s eta 0:00:00
Downloading pydeck-0.9.1-py2.py3-none-any.whl (6.9 MB)
   ---------------------------------------- 6.9/6.9 MB 11.5 MB/s eta 0:00:00
Downloading toml-0.10.2-py2.py3-none-any.whl (16 kB)
Downloading tornado-6.5.1-cp39-abi3-win_amd64.whl (444 kB)
Downloading watchdog-6.0.0-py3-none-win_amd64.whl (79 kB)
Downloading gitdb-4.0.12-py3-none-any.whl (62 kB)
Downloading jinja2-3.1.6-py3-none-any.whl (134 kB)
Downloading jsonschema-4.25.0-py3-none-any.whl (89 kB)
Downloading narwhals-2.0.1-py3-none-any.whl (385 kB)
Downloading pytz-2025.2-py2.py3-none-any.whl (509 kB)
Downloading tzdata-2025.2-py2.py3-none-any.whl (347 kB)
Downloading attrs-25.3.0-py3-none-any.whl (63 kB)
Downloading jsonschema_specifications-2025.4.1-py3-none-any.whl (18 kB)
Downloading MarkupSafe-3.0.2-cp312-cp312-win_amd64.whl (15 kB)
Downloading referencing-0.36.2-py3-none-any.whl (26 kB)
Downloading rpds_py-0.26.0-cp312-cp312-win_amd64.whl (234 kB)
Downloading smmap-5.0.2-py3-none-any.whl (24 kB)
Installing collected packages: pytz, watchdog, tzdata, tornado, toml, smmap, rpds-py, pyarrow, pillow, numpy, narwhals, MarkupSafe, click, cachetools, blinker, attrs, referencing, pandas, jinja2, gitdb, pydeck, jsonschema-specifications, gitpython, jsonschema, altair, streamlit
Successfully installed MarkupSafe-3.0.2 altair-5.5.0 attrs-25.3.0 blinker-1.9.0 cachetools-6.1.0 click-8.2.1 gitdb-4.0.12 gitpython-3.1.45 jinja2-3.1.6 jsonschema-4.25.0 jsonschema-specifications-2025.4.1 narwhals-2.0.1 numpy-2.3.2 pandas-2.3.1 pillow-11.3.0 pyarrow-21.0.0 pydeck-0.9.1 pytz-2025.2 referencing-0.36.2 rpds-py-0.26.0 smmap-5.0.2 streamlit-1.47.1 toml-0.10.2 tornado-6.5.1 tzdata-2025.2 watchdog-6.0.0
