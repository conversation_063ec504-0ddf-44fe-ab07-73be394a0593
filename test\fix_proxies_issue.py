#!/usr/bin/env python3
"""
修復 OpenAI 客戶端 proxies 參數問題
"""

import sys
import os

def apply_openai_fix():
    """應用 OpenAI 客戶端修復"""
    print("🔧 應用 OpenAI 客戶端修復...")
    
    try:
        # 方法1：Monkey patch OpenAI 客戶端
        from openai import AzureOpenAI, AsyncAzureOpenAI
        from openai._client import OpenAI, AsyncOpenAI
        
        # 保存原始的 __init__ 方法
        original_openai_init = OpenAI.__init__
        original_async_openai_init = AsyncOpenAI.__init__
        
        def patched_openai_init(self, **kwargs):
            # 移除 proxies 參數
            kwargs.pop('proxies', None)
            return original_openai_init(self, **kwargs)
        
        def patched_async_openai_init(self, **kwargs):
            # 移除 proxies 參數
            kwargs.pop('proxies', None)
            return original_async_openai_init(self, **kwargs)
        
        # 應用 patch
        OpenAI.__init__ = patched_openai_init
        AsyncOpenAI.__init__ = patched_async_openai_init
        
        print("✅ OpenAI 客戶端 monkey patch 已應用")
        return True
        
    except Exception as e:
        print(f"❌ Monkey patch 失敗: {e}")
        return False

def test_fixed_clients():
    """測試修復後的客戶端"""
    print("\n🧪 測試修復後的客戶端...")
    
    try:
        from openai import AzureOpenAI, AsyncAzureOpenAI
        
        # 測試同步客戶端
        client = AzureOpenAI(
            api_key="test_key",
            api_version="2023-05-15",
            azure_endpoint="https://test.openai.azure.com/"
        )
        print("✅ AzureOpenAI 同步客戶端創建成功")
        
        # 測試異步客戶端
        async_client = AsyncAzureOpenAI(
            api_key="test_key",
            api_version="2023-05-15",
            azure_endpoint="https://test.openai.azure.com/"
        )
        print("✅ AzureOpenAI 異步客戶端創建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def create_fixed_clients_module():
    """創建修復後的客戶端模組"""
    print("\n📝 創建修復後的客戶端模組...")
    
    fixed_module_code = '''"""
修復後的 OpenAI 客戶端模組
自動處理 proxies 參數問題
"""

from openai import AzureOpenAI as _AzureOpenAI, AsyncAzureOpenAI as _AsyncAzureOpenAI

class AzureOpenAI(_AzureOpenAI):
    """修復後的 AzureOpenAI 客戶端"""
    
    def __init__(self, **kwargs):
        # 移除可能導致問題的參數
        kwargs.pop('proxies', None)
        super().__init__(**kwargs)

class AsyncAzureOpenAI(_AsyncAzureOpenAI):
    """修復後的 AsyncAzureOpenAI 客戶端"""
    
    def __init__(self, **kwargs):
        # 移除可能導致問題的參數
        kwargs.pop('proxies', None)
        super().__init__(**kwargs)
'''
    
    try:
        with open('fixed_openai_clients.py', 'w', encoding='utf-8') as f:
            f.write(fixed_module_code)
        print("✅ 修復模組已創建: fixed_openai_clients.py")
        return True
    except Exception as e:
        print(f"❌ 創建模組失敗: {e}")
        return False

if __name__ == "__main__":
    print("🚀 OpenAI 客戶端修復工具")
    print("=" * 50)
    
    # 應用修復
    if apply_openai_fix():
        # 測試修復
        if test_fixed_clients():
            print("\n🎉 修復成功！現在可以正常使用 OpenAI 客戶端")
            
            # 創建修復模組供後續使用
            create_fixed_clients_module()
            
            print("\n📋 使用說明:")
            print("1. 在其他 Python 文件中，使用以下導入:")
            print("   from fixed_openai_clients import AzureOpenAI, AsyncAzureOpenAI")
            print("2. 或者在文件開頭調用 apply_openai_fix() 函數")
        else:
            print("\n❌ 修復測試失敗")
    else:
        print("\n❌ 修復應用失敗")
