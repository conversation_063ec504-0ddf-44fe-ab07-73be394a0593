#!/usr/bin/env python3
"""
Simple script to run the simplified Streamlit astrologer chat.
"""

import subprocess
import sys
import os

def main():
    """Run the simple chat interface."""
    print("🔮 Starting Simple AI Astrologer Chat")
    print("=" * 50)
    
    # Check if file exists
    if not os.path.exists("simple_astrologer_chat.py"):
        print("❌ simple_astrologer_chat.py not found")
        return
    
    print("🚀 Launching simple chat interface...")
    print("🌐 Opening in browser at http://localhost:8501")
    print("💬 Clean, ChatGPT-like interface")
    print("⚠️  Press Ctrl+C to stop")
    print("-" * 50)
    
    try:
        # Run Streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "simple_astrologer_chat.py",
            "--server.port", "8501",
            "--server.address", "localhost"
        ])
    except KeyboardInterrupt:
        print("\n👋 Chat stopped by user")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
