#!/usr/bin/env python3
"""
詳細診斷 OpenAI 客戶端問題
"""

import inspect
import sys

def debug_openai():
    print("🔍 詳細診斷 OpenAI 問題")
    print("=" * 60)
    
    try:
        import openai
        print(f"✅ OpenAI 版本: {openai.__version__}")
        print(f"📁 OpenAI 路徑: {openai.__file__}")
        
        # 檢查 AzureOpenAI 類的簽名
        from openai import AzureOpenAI
        print(f"\n🔍 檢查 AzureOpenAI.__init__ 簽名:")
        sig = inspect.signature(AzureOpenAI.__init__)
        print(f"參數: {list(sig.parameters.keys())}")
        
        # 嘗試查看文檔字符串
        if AzureOpenAI.__init__.__doc__:
            print(f"文檔: {AzureOpenAI.__init__.__doc__[:200]}...")
        
        # 嘗試不同的初始化方式
        print(f"\n🧪 測試不同的初始化方式:")
        
        # 方式1：最小參數
        try:
            client1 = AzureOpenAI(
                api_key="test_key",
                api_version="2023-05-15",
                azure_endpoint="https://test.openai.azure.com/"
            )
            print("✅ 方式1成功：基本參數")
        except Exception as e:
            print(f"❌ 方式1失敗：{e}")
        
        # 方式2：檢查是否有默認參數問題
        try:
            import os
            # 臨時清除可能的環境變數
            old_vars = {}
            for key in list(os.environ.keys()):
                if 'proxy' in key.lower():
                    old_vars[key] = os.environ.pop(key)
            
            client2 = AzureOpenAI(
                api_key="test_key",
                api_version="2023-05-15",
                azure_endpoint="https://test.openai.azure.com/"
            )
            print("✅ 方式2成功：清除proxy環境變數")
            
            # 恢復環境變數
            os.environ.update(old_vars)
            
        except Exception as e:
            print(f"❌ 方式2失敗：{e}")
            # 恢復環境變數
            os.environ.update(old_vars)
        
        # 方式3：檢查是否有全局配置
        try:
            # 檢查是否有全局的 openai 配置
            if hasattr(openai, 'api_key'):
                print(f"🔍 全局 api_key: {openai.api_key}")
            if hasattr(openai, 'api_base'):
                print(f"🔍 全局 api_base: {openai.api_base}")
            if hasattr(openai, 'proxy'):
                print(f"🔍 全局 proxy: {openai.proxy}")
                
        except Exception as e:
            print(f"⚠️  檢查全局配置時出錯: {e}")
        
        # 方式4：檢查 httpx 客戶端
        try:
            import httpx
            print(f"\n🔍 HTTPX 版本: {httpx.__version__}")
            
            # 嘗試直接創建 httpx 客戶端
            http_client = httpx.Client()
            print("✅ HTTPX 客戶端創建成功")
            
        except Exception as e:
            print(f"❌ HTTPX 客戶端問題: {e}")
            
    except Exception as e:
        print(f"❌ 導入 OpenAI 失敗: {e}")

def check_dependencies():
    print(f"\n🔍 檢查相關依賴:")
    
    dependencies = ['httpx', 'httpcore', 'anyio', 'certifi']
    
    for dep in dependencies:
        try:
            module = __import__(dep)
            version = getattr(module, '__version__', 'Unknown')
            print(f"✅ {dep}: {version}")
        except ImportError:
            print(f"❌ {dep}: 未安裝")
        except Exception as e:
            print(f"⚠️  {dep}: {e}")

def try_manual_fix():
    print(f"\n🔧 嘗試手動修復:")
    
    try:
        # 嘗試 monkey patch
        from openai import AzureOpenAI
        original_init = AzureOpenAI.__init__
        
        def patched_init(self, *args, **kwargs):
            # 移除 proxies 參數如果存在
            kwargs.pop('proxies', None)
            return original_init(self, *args, **kwargs)
        
        AzureOpenAI.__init__ = patched_init
        
        # 測試修復後的客戶端
        client = AzureOpenAI(
            api_key="test_key",
            api_version="2023-05-15",
            azure_endpoint="https://test.openai.azure.com/"
        )
        print("✅ Monkey patch 修復成功！")
        return True
        
    except Exception as e:
        print(f"❌ Monkey patch 失敗: {e}")
        return False

if __name__ == "__main__":
    debug_openai()
    check_dependencies()
    
    if try_manual_fix():
        print(f"\n🎉 找到解決方案！可以嘗試在代碼中應用 monkey patch")
    else:
        print(f"\n❌ 需要進一步調查問題根源")
