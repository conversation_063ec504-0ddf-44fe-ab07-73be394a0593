#!/usr/bin/env python3
"""
修復 OpenAI 套件版本兼容性問題
"""

import subprocess
import sys

def run_command(command):
    """執行命令並返回結果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def test_openai_import():
    """測試 OpenAI 導入"""
    try:
        from openai import AzureOpenAI
        print("✅ OpenAI 導入成功")
        return True
    except Exception as e:
        print(f"❌ OpenAI 導入失敗: {e}")
        return False

def main():
    print("🔧 修復 OpenAI 套件兼容性問題")
    print("=" * 50)
    
    # 測試當前版本
    if test_openai_import():
        print("✅ OpenAI 套件已經正常工作")
        return
    
    # 嘗試不同版本
    versions_to_try = [
        "1.35.0",
        "1.30.0", 
        "1.40.0",
        "1.25.0"
    ]
    
    for version in versions_to_try:
        print(f"\n🔄 嘗試安裝 OpenAI {version}...")
        
        # 卸載當前版本
        success, stdout, stderr = run_command("pip uninstall openai -y")
        if not success:
            print(f"⚠️  卸載失敗: {stderr}")
        
        # 安裝指定版本
        success, stdout, stderr = run_command(f"pip install openai=={version}")
        if not success:
            print(f"❌ 安裝 {version} 失敗: {stderr}")
            continue
        
        print(f"✅ 安裝 OpenAI {version} 成功")
        
        # 測試導入
        if test_openai_import():
            print(f"🎉 OpenAI {version} 工作正常！")
            
            # 測試 Azure 客戶端初始化
            try:
                from openai import AzureOpenAI
                # 使用測試參數
                client = AzureOpenAI(
                    api_key="test_key",
                    api_version="2023-05-15",
                    azure_endpoint="https://test.openai.azure.com/",
                )
                print("✅ AzureOpenAI 客戶端初始化成功")
                print(f"🎯 建議使用 OpenAI {version}")
                return
            except Exception as e:
                print(f"❌ AzureOpenAI 客戶端初始化失敗: {e}")
                continue
        else:
            print(f"❌ OpenAI {version} 仍有問題")
    
    print("\n❌ 所有版本都無法正常工作，請手動檢查環境")

if __name__ == "__main__":
    main()
