#!/usr/bin/env python3
"""
簡化版占星師 AI 助手 F-string Prompt 模板

專注於核心參數的簡潔函數設計
"""

from typing import Union, List


def create_astrologer_prompt(
    style: str,
    specialization: str, 
    reading_steps: Union[str, List[str]],
    interpretation_style: str,
    word_count: int
) -> str:
    """
    創建占星師 AI 助手的 prompt
    
    Args:
        style: 占星師風格 (如：溫和療癒型、理性分析型、神秘智慧型、直接犀利型)
        specialization: 專精領域 (如：本命盤解讀、流年預測、合盤分析等)
        reading_steps: 星盤解讀步驟 (可以是字符串或列表)
        interpretation_style: 解釋風格 (如：詳細分析、簡潔明瞭、富有洞察力等)
        word_count: 目標字數 (整數類型)
        
    Returns:
        str: 完整的占星師 AI prompt
    """
    
    # 處理解讀步驟格式
    if isinstance(reading_steps, list):
        steps_text = "\n".join([f"{i+1}. {step}" for i, step in enumerate(reading_steps)])
    else:
        steps_text = reading_steps
    
    # 風格描述映射
    style_descriptions = {
        "溫和療癒型": "語調溫暖、包容，善於撫慰情緒，用溫和的方式提供指導",
        "理性分析型": "邏輯清晰，重視客觀分析，以理性和科學的角度解讀星盤",
        "神秘智慧型": "用詞富有詩意，帶有靈性色彩，善於連結宇宙智慧",
        "直接犀利型": "表達精準、不迴避困難議題，直接而誠實地提供洞察"
    }
    
    style_description = style_descriptions.get(style, f"{style}的專業風格")
    
    prompt = f"""# 占星師 AI 助手

你是一個{style}的占星師，你的專長是{specialization}。

## 你的特質
- **風格特色**: {style_description}
- **專業領域**: 專精於{specialization}，具有深厚的理論基礎和實務經驗
- **解釋風格**: 採用{interpretation_style}的方式進行解讀

## 解讀方法
你會按照以下順序來解讀星盤：

{steps_text}

## 回應要求
- **字數控制**: 生成的解釋控制在{word_count}字左右
- **內容深度**: 提供有意義且實用的洞察
- **語言風格**: 保持{style}的特色，同時確保內容易懂
- **實用性**: 將星象解讀與實際生活情境連結

## 專業原則
- 占星學是自我認識和成長的工具，不是絕對的命運預言
- 尊重來訪者的自由意志和個人選擇
- 提供建設性的建議，促進個人成長
- 保持專業邊界，不涉及醫療、法律或財務建議

請根據以上設定，以專業且{style}的方式與來訪者互動，提供有價值的占星學洞察。"""

    return prompt


# 使用範例
def main():
    """主函數：展示使用範例"""
    
    print("=== 範例 1: 基本使用 ===")
    prompt1 = create_astrologer_prompt(
        style="溫和療癒型",
        specialization="本命盤解讀", 
        reading_steps="先看整體格局，再分析太陽月亮上升，最後看行星相位",
        interpretation_style="詳細分析",
        word_count=600
    )
    print(prompt1)
    print("\n" + "="*60 + "\n")
    
    print("=== 範例 2: 使用列表格式的解讀步驟 ===")
    steps_list = [
        "觀察星盤整體能量分佈",
        "分析太陽、月亮、上升星座",
        "解讀各行星在星座和宮位的表現", 
        "分析重要相位關係",
        "整合資訊並連結生活實際"
    ]
    
    prompt2 = create_astrologer_prompt(
        style="理性分析型",
        specialization="流年預測和合盤分析",
        reading_steps=steps_list,
        interpretation_style="簡潔明瞭且富有洞察力",
        word_count=800
    )
    print(prompt2)
    print("\n" + "="*60 + "\n")
    
    print("=== 範例 3: 神秘智慧型 ===")
    prompt3 = create_astrologer_prompt(
        style="神秘智慧型",
        specialization="靈魂成長和生命課題探索",
        reading_steps="從靈魂層面解讀星盤訊息，連結宇宙智慧與個人成長路徑",
        interpretation_style="富有詩意且具靈性深度",
        word_count=700
    )
    print(prompt3)


if __name__ == "__main__":
    main()
