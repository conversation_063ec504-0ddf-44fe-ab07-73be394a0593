# 占星師 AI Prompt 模板使用指南

## 概述

這個 JSON 模板系統讓你可以輕鬆客製化占星師 AI 的特性，無需直接編輯複雜的 prompt 文本。

## 文件結構

```
prompts/
├── astrologer_prompt_template.json    # 主要模板文件
├── prompt_generator.py               # Python 生成器腳本
├── usage_examples.md                # 使用說明（本文件）
└── generated_prompts/               # 生成的 prompt 文件夾
```

## 快速開始

### 1. 使用 Python 腳本生成

```python
from prompt_generator import PromptGenerator, generate_custom_prompt

# 方法一：使用類
generator = PromptGenerator()
prompt = generator.generate_prompt({
    "communication_tone": "溫和療癒型",
    "specialization_areas": ["本命盤解讀", "流年預測"],
    "emotional_expression": "溫暖共情"
})

# 方法二：使用便捷函數
prompt = generate_custom_prompt(
    communication_tone="理性分析型",
    specialization_areas=["合盤分析"],
    emotional_expression="冷靜客觀"
)
```

### 2. 直接修改 JSON 文件

編輯 `astrologer_prompt_template.json` 中的 `variables` 部分：

```json
{
  "variables": {
    "communication_tone": {
      "default": "溫和療癒型"  // 修改這裡
    },
    "specialization_areas": {
      "default": ["本命盤解讀", "合盤分析"]  // 修改這裡
    }
  }
}
```

## 變數類型說明

### 1. Select（單選）
```json
"communication_tone": {
  "type": "select",
  "default": "溫和療癒型",
  "options": ["溫和療癒型", "直接犀利型", "神秘智慧型", "理性分析型"]
}
```

### 2. Multiselect（多選）
```json
"specialization_areas": {
  "type": "multiselect", 
  "default": ["本命盤解讀", "流年預測"],
  "options": ["本命盤解讀", "流年預測", "合盤分析", "卜卦占星", "擇時占星"]
}
```

### 3. Text（文本）
```json
"knowledge_system": {
  "type": "text",
  "default": "主要體系：西洋占星學，技法整合：古典技法與現代心理學方法並用"
}
```

### 4. Array（陣列）
```json
"reading_sequence": {
  "type": "array",
  "default": [
    "整體格局：先觀察星盤的整體能量分佈",
    "核心主題：太陽、月亮、上升星座的基礎人格"
  ]
}
```

### 5. Object（物件）
```json
"life_stage_focus": {
  "type": "object",
  "default": {
    "青少年": "重視自我認同和潛能發展",
    "青年期": "關注事業、感情和人生方向"
  }
}
```

## 常見配置範例

### 範例 1：溫和療癒型占星師
```python
healing_astrologer = generate_custom_prompt(
    communication_tone="溫和療癒型",
    emotional_expression="溫暖共情",
    specialization_areas=["本命盤解讀", "流年預測"],
    interaction_methods=["引導式提問", "主動解讀"]
)
```

### 範例 2：理性分析型占星師
```python
analytical_astrologer = generate_custom_prompt(
    communication_tone="理性分析型",
    emotional_expression="冷靜客觀", 
    specialization_areas=["合盤分析", "擇時占星"],
    astrology_school="古典占星"
)
```

### 範例 3：神秘智慧型占星師
```python
mystical_astrologer = generate_custom_prompt(
    communication_tone="神秘智慧型",
    emotional_expression="鼓勵支持",
    specialization_areas=["本命盤解讀", "卜卦占星"],
    astrology_school="進化占星"
)
```

### 範例 4：直接犀利型占星師
```python
direct_astrologer = generate_custom_prompt(
    communication_tone="直接犀利型",
    emotional_expression="冷靜客觀",
    specialization_areas=["流年預測", "財經占星"],
    interaction_methods=["回應式諮詢"]
)
```

## 進階使用

### 1. 批量生成不同風格的 prompt

```python
from prompt_generator import PromptGenerator

generator = PromptGenerator()

styles = [
    {"name": "療癒師", "tone": "溫和療癒型", "expression": "溫暖共情"},
    {"name": "分析師", "tone": "理性分析型", "expression": "冷靜客觀"},
    {"name": "智者", "tone": "神秘智慧型", "expression": "鼓勵支持"},
    {"name": "顧問", "tone": "直接犀利型", "expression": "冷靜客觀"}
]

for style in styles:
    prompt = generator.generate_prompt({
        "communication_tone": style["tone"],
        "emotional_expression": style["expression"]
    })
    
    filename = f"generated_prompts/{style['name']}_astrologer.txt"
    generator.save_prompt(filename, {
        "communication_tone": style["tone"],
        "emotional_expression": style["expression"]
    })
```

### 2. 動態配置檢查

```python
generator = PromptGenerator()

# 檢查變數資訊
print("可用變數:")
for var_name in generator.list_variables():
    var_info = generator.get_variable_info(var_name)
    print(f"- {var_name}: {var_info['name']} ({var_info['type']})")

# 檢查預設值
defaults = generator.get_default_values()
print(f"\n預設配置: {defaults}")
```

## 自定義擴展

### 1. 添加新變數

在 JSON 文件的 `variables` 部分添加：

```json
"new_variable": {
  "type": "select",
  "name": "新變數名稱",
  "description": "變數描述",
  "default": "預設值",
  "options": ["選項1", "選項2", "選項3"]
}
```

### 2. 修改模板內容

在 JSON 文件的 `template` 部分使用新變數：

```
### 新章節
{{new_variable}}
```

## 注意事項

1. **變數命名**: 使用英文變數名，避免特殊字符
2. **JSON 格式**: 確保 JSON 格式正確，字串使用雙引號
3. **變數引用**: 在模板中使用 `{{variable_name}}` 格式引用變數
4. **物件訪問**: 使用 `{{object_name[key_name]}}` 格式訪問物件屬性
5. **編碼**: 文件使用 UTF-8 編碼保存

## 故障排除

### 常見錯誤

1. **JSON 格式錯誤**: 檢查括號、逗號、引號是否正確
2. **變數未找到**: 確認變數名稱拼寫正確
3. **編碼問題**: 確保文件以 UTF-8 編碼保存
4. **路徑錯誤**: 檢查文件路徑是否正確

### 調試技巧

```python
# 檢查生成的 prompt 是否包含未替換的變數
prompt = generator.generate_prompt(custom_values)
if "{{" in prompt:
    print("警告：發現未替換的變數")
    # 找出未替換的變數
    import re
    unresolved = re.findall(r'\{\{([^}]+)\}\}', prompt)
    print(f"未解析的變數: {unresolved}")
```

這個模板系統讓你可以輕鬆創建各種風格的占星師 AI，滿足不同場景和用戶需求。
