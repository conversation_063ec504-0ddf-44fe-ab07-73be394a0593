#!/usr/bin/env python3
"""
Enhanced Interactive Astrology RAG Testing System
Compares three response modes: No RAG, Simple Chunking RAG, and Hierarchical Chunking RAG
"""

import asyncio
import time
import sys
import re
import os
from typing import Dict, List, Optional, Tuple
from datetime import datetime

from pinecone_client import PineconeClient
from gpt4o_client import GPT4oClient
from config import config


class RAGLogger:
    """Logger class for simultaneous terminal and file output for RAG testing"""

    def __init__(self, enable_logging: bool = True):
        """Initialize logger with optional file logging"""
        self.enable_logging = enable_logging
        self.log_file = None
        self.log_filename = None

        if self.enable_logging:
            self._create_log_file()

    def _create_log_file(self):
        """Create log file with timestamp"""
        # Create logs directory if it doesn't exist
        logs_dir = "logs"
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)

        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_filename = os.path.join(logs_dir, f"RAG_test_log_{timestamp}.txt")

        try:
            self.log_file = open(self.log_filename, 'w', encoding='utf-8')
            # Write header
            self.log_file.write(f"Enhanced Astrology RAG Testing System 日誌\n")
            self.log_file.write(f"開始時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            self.log_file.write("=" * 60 + "\n\n")
            self.log_file.flush()
        except Exception as e:
            print(f"⚠️  無法創建日誌文件: {e}")
            self.enable_logging = False

    def print(self, *args, **kwargs):
        """Print to both terminal and log file"""
        # Print to terminal
        print(*args, **kwargs)

        # Write to log file if enabled
        if self.enable_logging and self.log_file:
            try:
                # Convert args to string and write to file
                message = ' '.join(str(arg) for arg in args)
                self.log_file.write(message + '\n')
                self.log_file.flush()
            except Exception as e:
                print(f"⚠️  日誌寫入失敗: {e}")

    def log_separator(self, title: str = ""):
        """Add a separator line to the log"""
        separator = "=" * 100
        if title:
            self.print(f"\n{separator}")
            self.print(f"📋 {title}")
            self.print(separator)
        else:
            self.print(separator)

    def log_test_start(self, test_type: str, question: str = ""):
        """Log the start of a test"""
        self.log_separator(f"開始 {test_type}")
        self.print(f"⏰ 時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        if question:
            self.print(f"❓ 問題: {question}")
        self.print()

    def log_test_end(self, test_type: str):
        """Log the end of a test"""
        self.log_separator(f"結束 {test_type}")
        self.print(f"⏰ 時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.print()

    def close(self):
        """Close the log file"""
        if self.log_file:
            try:
                self.log_file.write(f"\n結束時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                self.log_file.write("=" * 60 + "\n")
                self.log_file.close()
                return self.log_filename
            except Exception as e:
                print(f"⚠️  關閉日誌文件失敗: {e}")
        return None

    def get_log_filename(self):
        """Get the current log filename"""
        return self.log_filename


class AstrologyRAGTester:
    """
    Comprehensive testing system for comparing three different response modes:
    1. No RAG (baseline GPT-4o responses)
    2. RAG with simple chunking strategy
    3. RAG with hierarchical chunking strategy
    """

    def __init__(self, logger: RAGLogger = None):
        """Initialize the testing system components"""
        # Initialize logger
        self.logger = logger or RAGLogger(enable_logging=False)

        self.logger.print("🚀 Initializing Enhanced Astrology RAG Testing System...")

        # Initialize clients
        self.pinecone_client = PineconeClient()
        self.gpt4o_client = GPT4oClient()

        # System configuration
        self.index_name = config.PINECONE_INDEX_NAME or "astrology-text"
        self.simple_namespace = "simple_chunking_strategy"
        self.hierarchical_namespace = "hierarchical_chunking_strategy"

        # System prompt for astrology expertise
        self.system_prompt = """You are a professional astrology expert specializing in interpreting zodiac signs, planets, houses, and astrological concepts.
Please provide accurate, detailed, and helpful information about astrology in a professional yet accessible manner.
Your responses should be well-structured, informative, and concise while maintaining depth."""

        # Test results history
        self.test_history = []

        # Predefined test questions
        self.test_questions = self._create_test_question_set()

        # Verbose mode flag for detailed metadata
        self.verbose_mode = False

        # Similarity threshold configuration
        self.similarity_thresholds = {
            "simple_rag": 0.6,      # Default threshold for simple chunking
            "hierarchical_rag": 0.60, # Slightly lower for hierarchical (has paragraph context)
            "global_default": 0.6,   # Global fallback threshold
            "minimum_chunks": 1       # Minimum chunks required even if below threshold
        }

        self.logger.print(f"📊 System Configuration:")
        self.logger.print(f"   - Pinecone Index: {self.index_name}")
        self.logger.print(f"   - Simple Namespace: {self.simple_namespace}")
        self.logger.print(f"   - Hierarchical Namespace: {self.hierarchical_namespace}")
        self.logger.print(f"   - Pinecone Available: {self.pinecone_client._pinecone_available}")
        self.logger.print(f"   - Test Questions: {len(self.test_questions)} predefined")
        if self.logger.enable_logging:
            self.logger.print(f"   - 日誌文件: {self.logger.get_log_filename()}")
        self.logger.print()

    def _create_test_question_set(self) -> List[Dict[str, str]]:
        """Create a predefined set of astrology-related test questions"""
        return [
            {
                "category": "Basic Concepts",
                "question": "What are the key characteristics of people with Venus in Gemini?",
                "complexity": "basic"
            },
            {
                "category": "House Interpretation",
                "question": "What does the 12th house represent in astrology?",
                "complexity": "basic"
            },
            {
                "category": "Planetary Aspects",
                "question": "How does a Mars-Jupiter trine aspect influence a person's personality?",
                "complexity": "intermediate"
            },
            {
                "category": "Rising Signs",
                "question": "What impression do people with Scorpio rising typically give to others?",
                "complexity": "intermediate"
            },
            {
                "category": "Chart Interpretation",
                "question": "How do you interpret a stellium in the 7th house?",
                "complexity": "advanced"
            },
            {
                "category": "Planetary Dignities",
                "question": "What does it mean when a planet is in its detriment or fall?",
                "complexity": "advanced"
            },
            {
                "category": "Transits",
                "question": "What can someone expect during a Saturn return?",
                "complexity": "intermediate"
            },
            {
                "category": "Elements and Modalities",
                "question": "How do fire signs differ from earth signs in their approach to life?",
                "complexity": "basic"
            }
        ]

    def _apply_similarity_threshold(self, matches: List[Dict], strategy: str, custom_threshold: float = None) -> Tuple[List[Dict], Dict]:
        """
        Apply similarity threshold filtering to retrieved matches

        Args:
            matches: List of matches from Pinecone
            strategy: Strategy name ('simple_rag' or 'hierarchical_rag')
            custom_threshold: Optional custom threshold for this query

        Returns:
            Tuple of (filtered_matches, filtering_stats)
        """
        if not matches:
            return [], {"original_count": 0, "filtered_count": 0, "threshold_used": 0, "below_threshold": 0}

        # Determine threshold to use
        threshold = custom_threshold or self.similarity_thresholds.get(strategy, self.similarity_thresholds["global_default"])
        minimum_chunks = self.similarity_thresholds["minimum_chunks"]

        # Separate matches above and below threshold
        above_threshold = [match for match in matches if match.get("score", 0) >= threshold]
        below_threshold = [match for match in matches if match.get("score", 0) < threshold]

        # Apply minimum chunks requirement
        if len(above_threshold) < minimum_chunks and matches:
            # If we don't have enough above threshold, take the top matches regardless
            filtered_matches = sorted(matches, key=lambda x: x.get("score", 0), reverse=True)[:minimum_chunks]
            fallback_used = True
        else:
            filtered_matches = above_threshold
            fallback_used = False

        # Create filtering statistics
        filtering_stats = {
            "original_count": len(matches),
            "filtered_count": len(filtered_matches),
            "threshold_used": threshold,
            "below_threshold": len(below_threshold),
            "fallback_used": fallback_used,
            "lowest_score_kept": min([m.get("score", 0) for m in filtered_matches]) if filtered_matches else 0,
            "highest_score_rejected": max([m.get("score", 0) for m in below_threshold]) if below_threshold else 0
        }

        return filtered_matches, filtering_stats

    async def get_no_rag_response(self, question: str) -> Dict:
        """Get baseline GPT-4o response without RAG"""
        start_time = time.time()

        try:
            response = await self.gpt4o_client.generate_response(
                system_prompt=self.system_prompt,
                user_input=question,
                rag_context=None,
                temperature=0.7
            )

            response_time = time.time() - start_time
            # Calculate word count for both English and Chinese text
            # Count English words (separated by spaces) + Chinese characters
            english_words = len(re.findall(r'\b[a-zA-Z]+\b', response))
            chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', response))
            word_count = english_words + chinese_chars

            return {
                "response": response,
                "response_time": response_time,
                "word_count": word_count,
                "mode": "No RAG",
                "success": True
            }

        except Exception as e:
            return {
                "response": f"Error: {str(e)}",
                "response_time": 0,
                "word_count": 0,
                "mode": "No RAG",
                "success": False
            }

    async def get_simple_rag_response(self, question: str) -> Dict:
        """Get RAG response using simple chunking strategy"""
        return await self._get_rag_response(question, self.simple_namespace, "Simple RAG")

    async def get_hierarchical_rag_response(self, question: str) -> Dict:
        """Get RAG response using hierarchical chunking strategy"""
        return await self._get_rag_response(question, self.hierarchical_namespace, "Hierarchical RAG")

    async def _get_rag_response(self, question: str, namespace: str, mode: str, custom_threshold: float = None) -> Dict:
        """Internal method to get RAG response from specified namespace with similarity threshold filtering"""
        start_time = time.time()

        try:
            # Search for relevant knowledge
            rag_context = None
            rag_sources = []
            rag_scores = []
            source_citations = []
            filtering_stats = {}

            if self.pinecone_client._pinecone_available:
                # Get initial matches from Pinecone
                raw_matches = self.pinecone_client.query_vectors(
                    query=question,
                    index_name=self.index_name,
                    namespace=namespace,
                    top_k=5  # Get more initially, then filter
                )

                # Apply similarity threshold filtering
                strategy_key = "simple_rag" if "simple" in mode.lower() else "hierarchical_rag"
                matches, filtering_stats = self._apply_similarity_threshold(raw_matches, strategy_key, custom_threshold)

                if matches:
                    rag_context = []
                    detailed_chunks = []
                    chunk_indices = []

                    for match in matches:
                        score = match.get("score", 0)
                        metadata = match.get("metadata", {})
                        source_file = metadata.get("source_file", "unknown")
                        chunk_text = metadata.get("chunk_text", "")
                        chunk_index = metadata.get("chunk_index", 0)
                        paragraph_index = metadata.get("paragraph_index", None)
                        paragraph_context = metadata.get("paragraph_context", "")

                        rag_scores.append(score)
                        chunk_indices.append(chunk_index)

                        if source_file not in rag_sources:
                            rag_sources.append(source_file)

                        # Create detailed citation
                        citation = f"{source_file} (score: {score:.3f})"
                        if citation not in source_citations:
                            source_citations.append(citation)

                        # Store detailed chunk information
                        chunk_info = {
                            "score": score,
                            "source_file": source_file,
                            "chunk_index": chunk_index,
                            "chunk_text": chunk_text,
                            "chunk_preview": chunk_text[:100] + "..." if len(chunk_text) > 100 else chunk_text
                        }

                        # Add hierarchical-specific metadata
                        if paragraph_index is not None:
                            chunk_info["paragraph_index"] = paragraph_index
                            chunk_info["paragraph_context"] = paragraph_context
                            chunk_info["paragraph_preview"] = paragraph_context[:100] + "..." if len(paragraph_context) > 100 else paragraph_context

                        detailed_chunks.append(chunk_info)

                        # Add to context
                        rag_context.append({
                            "question": metadata.get("question", ""),
                            "answer": metadata.get("chunk_text", ""),
                            "score": score,
                            "metadata": metadata
                        })

            # Generate response
            response = await self.gpt4o_client.generate_response(
                system_prompt=self.system_prompt,
                user_input=question,
                rag_context=rag_context,
                temperature=0.7
            )

            response_time = time.time() - start_time
            # Calculate word count for both English and Chinese text
            # Count English words (separated by spaces) + Chinese characters
            english_words = len(re.findall(r'\b[a-zA-Z]+\b', response))
            chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', response))
            word_count = english_words + chinese_chars

            # Calculate similarity statistics
            similarity_stats = {}
            if rag_scores:
                import statistics
                similarity_stats = {
                    "min": min(rag_scores),
                    "max": max(rag_scores),
                    "avg": sum(rag_scores) / len(rag_scores),
                    "std": statistics.stdev(rag_scores) if len(rag_scores) > 1 else 0
                }

            return {
                "response": response,
                "response_time": response_time,
                "word_count": word_count,
                "rag_sources": rag_sources,
                "rag_scores": rag_scores,
                "source_citations": source_citations,
                "rag_context_count": len(rag_context) if rag_context else 0,
                "mode": mode,
                "namespace": namespace,
                "success": True,
                # Enhanced metadata
                "detailed_chunks": detailed_chunks if 'detailed_chunks' in locals() else [],
                "chunk_indices": chunk_indices if 'chunk_indices' in locals() else [],
                "similarity_stats": similarity_stats,
                "top_3_scores": rag_scores[:3] if rag_scores else [],
                # Threshold filtering metadata
                "filtering_stats": filtering_stats,
                "threshold_applied": True
            }

        except Exception as e:
            return {
                "response": f"Error: {str(e)}",
                "response_time": 0,
                "word_count": 0,
                "rag_sources": [],
                "rag_scores": [],
                "source_citations": [],
                "rag_context_count": 0,
                "mode": mode,
                "namespace": namespace,
                "success": False,
                "detailed_chunks": [],
                "chunk_indices": [],
                "similarity_stats": {},
                "top_3_scores": [],
                "filtering_stats": {},
                "threshold_applied": False
            }

    async def compare_all_responses(self, question: str) -> Dict:
        """Compare responses from all three modes"""
        self.logger.log_test_start("三模式比較測試", question)
        self.logger.print(f"\n🤔 Question: {question}")
        self.logger.print("=" * 100)

        # Generate responses from all three modes in parallel
        self.logger.print("🔄 Generating responses from all three modes...")
        no_rag_task = asyncio.create_task(self.get_no_rag_response(question))
        simple_rag_task = asyncio.create_task(self.get_simple_rag_response(question))
        hierarchical_rag_task = asyncio.create_task(self.get_hierarchical_rag_response(question))

        no_rag_result, simple_rag_result, hierarchical_rag_result = await asyncio.gather(
            no_rag_task, simple_rag_task, hierarchical_rag_task
        )

        self.logger.print("✅ All responses generated!")
        self.logger.print()

        # Create comparison result
        comparison_result = {
            "question": question,
            "no_rag": no_rag_result,
            "simple_rag": simple_rag_result,
            "hierarchical_rag": hierarchical_rag_result,
            "timestamp": time.time()
        }

        # Display side-by-side comparison
        self._display_comparison_table(comparison_result)

        # Display metadata summary and overlap analysis
        self._display_metadata_summary(comparison_result)

        # Save to test history
        self.test_history.append(comparison_result)

        self.logger.log_test_end("三模式比較測試")

        return comparison_result

    def _display_comparison_table(self, result: Dict) -> None:
        """Display a formatted comparison table with enhanced RAG metadata"""
        self.logger.print("\n📊 RESPONSE COMPARISON TABLE")
        self.logger.print("=" * 120)

        # Extract results
        no_rag = result["no_rag"]
        simple_rag = result["simple_rag"]
        hierarchical_rag = result["hierarchical_rag"]

        # Header
        self.logger.print(f"{'Aspect':<20} {'No RAG':<30} {'Simple RAG':<30} {'Hierarchical RAG':<30}")
        self.logger.print("-" * 120)

        # Response times
        self.logger.print(f"{'Response Time':<20} {no_rag['response_time']:.2f}s{'':<22} {simple_rag['response_time']:.2f}s{'':<22} {hierarchical_rag['response_time']:.2f}s")

        # Word counts
        self.logger.print(f"{'Word Count':<20} {no_rag['word_count']:<30} {simple_rag['word_count']:<30} {hierarchical_rag['word_count']}")

        # Success status
        self.logger.print(f"{'Success':<20} {'✅' if no_rag['success'] else '❌':<30} {'✅' if simple_rag['success'] else '❌':<30} {'✅' if hierarchical_rag['success'] else '❌'}")

        # RAG-specific info
        if simple_rag['success']:
            simple_sources = len(simple_rag.get('rag_sources', []))
            simple_contexts = simple_rag.get('rag_context_count', 0)
            simple_chunks = simple_rag.get('chunk_indices', [])
        else:
            simple_sources = simple_contexts = 0
            simple_chunks = []

        if hierarchical_rag['success']:
            hier_sources = len(hierarchical_rag.get('rag_sources', []))
            hier_contexts = hierarchical_rag.get('rag_context_count', 0)
            hier_chunks = hierarchical_rag.get('chunk_indices', [])
        else:
            hier_sources = hier_contexts = 0
            hier_chunks = []

        self.logger.print(f"{'Sources Found':<20} {'N/A':<30} {simple_sources:<30} {hier_sources}")
        self.logger.print(f"{'Context Chunks':<20} {'N/A':<30} {simple_contexts:<30} {hier_contexts}")

        # Chunk indices (first 3)
        simple_chunk_str = str(simple_chunks[:3]) if simple_chunks else "N/A"
        hier_chunk_str = str(hier_chunks[:3]) if hier_chunks else "N/A"
        self.logger.print(f"{'Chunk Indices':<20} {'N/A':<30} {simple_chunk_str:<30} {hier_chunk_str}")

        # Top 3 similarity scores
        simple_top3 = simple_rag.get('top_3_scores', [])
        hier_top3 = hierarchical_rag.get('top_3_scores', [])
        simple_scores_str = f"[{', '.join([f'{s:.3f}' for s in simple_top3])}]" if simple_top3 else "N/A"
        hier_scores_str = f"[{', '.join([f'{s:.3f}' for s in hier_top3])}]" if hier_top3 else "N/A"
        self.logger.print(f"{'Top 3 Scores':<20} {'N/A':<30} {simple_scores_str:<30} {hier_scores_str}")

        # Source files (truncated)
        simple_sources_list = simple_rag.get('rag_sources', [])
        hier_sources_list = hierarchical_rag.get('rag_sources', [])

        def truncate_filename(filename, max_len=25):
            if len(filename) <= max_len:
                return filename
            return filename[:max_len-3] + "..."

        simple_source_str = truncate_filename(simple_sources_list[0]) if simple_sources_list else "N/A"
        hier_source_str = truncate_filename(hier_sources_list[0]) if hier_sources_list else "N/A"
        self.logger.print(f"{'Primary Source':<20} {'N/A':<30} {simple_source_str:<30} {hier_source_str}")

        # Similarity statistics
        simple_stats = simple_rag.get('similarity_stats', {})
        hier_stats = hierarchical_rag.get('similarity_stats', {})

        simple_avg = simple_stats.get('avg', 0)
        hier_avg = hier_stats.get('avg', 0)
        print(f"{'Avg Similarity':<20} {'N/A':<30} {simple_avg:.3f}{'':<26} {hier_avg:.3f}")

        # Threshold filtering information
        simple_filtering = simple_rag.get('filtering_stats', {})
        hier_filtering = hierarchical_rag.get('filtering_stats', {})

        simple_threshold = simple_filtering.get('threshold_used', 0)
        hier_threshold = hier_filtering.get('threshold_used', 0)
        print(f"{'Threshold Used':<20} {'N/A':<30} {simple_threshold:.3f}{'':<26} {hier_threshold:.3f}")

        simple_filtered = f"{simple_filtering.get('filtered_count', 0)}/{simple_filtering.get('original_count', 0)}"
        hier_filtered = f"{hier_filtering.get('filtered_count', 0)}/{hier_filtering.get('original_count', 0)}"
        print(f"{'Chunks (Used/Total)':<20} {'N/A':<30} {simple_filtered:<30} {hier_filtered}")

        print("-" * 120)

    def _display_detailed_responses(self, result: Dict) -> None:
        """Display detailed responses from all three modes with enhanced metadata"""
        self.logger.print("\n📝 DETAILED RESPONSES")
        self.logger.print("=" * 120)

        modes = [
            ("no_rag", "🤖 NO RAG RESPONSE"),
            ("simple_rag", "🔍 SIMPLE RAG RESPONSE"),
            ("hierarchical_rag", "🧠 HIERARCHICAL RAG RESPONSE")
        ]

        for mode_key, mode_title in modes:
            mode_result = result[mode_key]
            self.logger.print(f"\n{mode_title}")
            self.logger.print(f"⏱️  Time: {mode_result['response_time']:.2f}s | 📝 Words: {mode_result['word_count']}")

            if mode_key != "no_rag" and mode_result['success']:
                self._display_rag_metadata(mode_result, mode_key)

            self.logger.print("-" * 120)
            if mode_result['success']:
                response_text = mode_result['response']
                self.logger.print(response_text)
            else:
                self.logger.print(f"❌ {mode_result['response']}")
            self.logger.print()

    def _display_rag_metadata(self, mode_result: Dict, mode_key: str) -> None:
        """Display detailed RAG metadata for a specific mode"""
        if mode_result.get('rag_context_count', 0) > 0:
            self.logger.print(f"📚 Found {mode_result['rag_context_count']} relevant chunks")

            # Threshold filtering information
            filtering_stats = mode_result.get('filtering_stats', {})
            if filtering_stats:
                threshold = filtering_stats.get('threshold_used', 0)
                original_count = filtering_stats.get('original_count', 0)
                filtered_count = filtering_stats.get('filtered_count', 0)
                below_threshold = filtering_stats.get('below_threshold', 0)
                fallback_used = filtering_stats.get('fallback_used', False)

                self.logger.print(f"🎯 Threshold Filter: {threshold:.3f} | Retrieved: {original_count} | Used: {filtered_count} | Rejected: {below_threshold}")
                if fallback_used:
                    self.logger.print(f"⚠️  Fallback applied: Minimum chunks requirement used")

            # Similarity statistics
            stats = mode_result.get('similarity_stats', {})
            if stats:
                print(f"� Similarity Stats: Min: {stats.get('min', 0):.3f} | Max: {stats.get('max', 0):.3f} | Avg: {stats.get('avg', 0):.3f} | Std: {stats.get('std', 0):.3f}")

            # Complete source citations
            if mode_result.get('source_citations'):
                print(f"📖 Complete Sources:")
                for i, citation in enumerate(mode_result['source_citations'], 1):
                    print(f"   {i}. {citation}")

            # Detailed chunk information (if verbose mode or first 3)
            detailed_chunks = mode_result.get('detailed_chunks', [])
            if detailed_chunks:
                display_count = len(detailed_chunks) if self.verbose_mode else min(3, len(detailed_chunks))
                self.logger.print(f"🔍 Chunk Details (showing {display_count}/{len(detailed_chunks)}):")

                for i, chunk in enumerate(detailed_chunks[:display_count], 1):
                    self.logger.print(f"   Chunk {i}:")
                    self.logger.print(f"     📄 Source: {chunk.get('source_file', 'unknown')}")
                    self.logger.print(f"     🔢 Index: {chunk.get('chunk_index', 'N/A')}")
                    self.logger.print(f"     🎯 Score: {chunk.get('score', 0):.3f}")

                    # Hierarchical-specific info
                    if 'paragraph_index' in chunk:
                        self.logger.print(f"     📑 Paragraph: {chunk.get('paragraph_index', 'N/A')}")
                        if self.verbose_mode and chunk.get('paragraph_context'):
                            self.logger.print(f"     📝 Para Context: {chunk['paragraph_context']}")

                    # Chunk preview
                    if chunk.get('chunk_preview'):
                        self.logger.print(f"     💬 Preview: {chunk['chunk_preview']}")

                    # Verbose mode: show full chunk text
                    if self.verbose_mode and chunk.get('chunk_text'):
                        self.logger.print(f"     📄 Full Text: {chunk['chunk_text']}")

                    self.logger.print()

                if not self.verbose_mode and len(detailed_chunks) > 3:
                    self.logger.print(f"     ... and {len(detailed_chunks) - 3} more chunks (use /verbose for full details)")
        else:
            self.logger.print("⚠️  No relevant context found")

    def _display_metadata_summary(self, result: Dict) -> None:
        """Display metadata summary and overlap analysis between RAG strategies"""
        self.logger.print("\n🔍 METADATA SUMMARY & OVERLAP ANALYSIS")
        self.logger.print("=" * 120)

        simple_rag = result["simple_rag"]
        hierarchical_rag = result["hierarchical_rag"]

        # Extract source information
        simple_sources = set(simple_rag.get('rag_sources', []))
        hier_sources = set(hierarchical_rag.get('rag_sources', []))

        # Extract chunk information
        simple_chunks = simple_rag.get('detailed_chunks', [])
        hier_chunks = hierarchical_rag.get('detailed_chunks', [])

        # Source overlap analysis
        all_sources = simple_sources.union(hier_sources)
        overlapping_sources = simple_sources.intersection(hier_sources)

        print(f"📚 SOURCE ANALYSIS:")
        print(f"   Total unique sources found: {len(all_sources)}")
        print(f"   Simple RAG sources: {len(simple_sources)}")
        print(f"   Hierarchical RAG sources: {len(hier_sources)}")
        print(f"   Overlapping sources: {len(overlapping_sources)}")

        if overlapping_sources:
            print(f"   Common sources: {', '.join([s[:30] + '...' if len(s) > 30 else s for s in overlapping_sources])}")

        # Quality metrics comparison
        print(f"\n📊 QUALITY METRICS COMPARISON:")

        simple_stats = simple_rag.get('similarity_stats', {})
        hier_stats = hierarchical_rag.get('similarity_stats', {})

        if simple_stats and hier_stats:
            print(f"   Simple RAG - Avg Score: {simple_stats.get('avg', 0):.3f} | Max: {simple_stats.get('max', 0):.3f}")
            print(f"   Hierarchical RAG - Avg Score: {hier_stats.get('avg', 0):.3f} | Max: {hier_stats.get('max', 0):.3f}")

            # Determine which strategy found more relevant context
            simple_avg = simple_stats.get('avg', 0)
            hier_avg = hier_stats.get('avg', 0)

            if simple_avg > hier_avg:
                print(f"   🏆 Simple RAG found more relevant context (Δ: +{simple_avg - hier_avg:.3f})")
            elif hier_avg > simple_avg:
                print(f"   🏆 Hierarchical RAG found more relevant context (Δ: +{hier_avg - simple_avg:.3f})")
            else:
                print(f"   🤝 Both strategies found equally relevant context")

        # Chunk overlap analysis
        if simple_chunks and hier_chunks:
            simple_chunk_indices = set(chunk.get('chunk_index', -1) for chunk in simple_chunks)
            hier_chunk_indices = set(chunk.get('chunk_index', -1) for chunk in hier_chunks)

            overlapping_chunks = simple_chunk_indices.intersection(hier_chunk_indices)

            print(f"\n🧩 CHUNK OVERLAP ANALYSIS:")
            print(f"   Simple RAG chunks: {len(simple_chunk_indices)}")
            print(f"   Hierarchical RAG chunks: {len(hier_chunk_indices)}")
            print(f"   Overlapping chunk indices: {len(overlapping_chunks)}")

            if overlapping_chunks:
                overlap_percentage = (len(overlapping_chunks) / max(len(simple_chunk_indices), len(hier_chunk_indices))) * 100
                print(f"   Overlap percentage: {overlap_percentage:.1f}%")

        # Threshold filtering analysis
        print(f"\n🎯 THRESHOLD FILTERING ANALYSIS:")

        simple_filtering = simple_rag.get('filtering_stats', {})
        hier_filtering = hierarchical_rag.get('filtering_stats', {})

        if simple_filtering:
            simple_efficiency = (simple_filtering.get('filtered_count', 0) / max(simple_filtering.get('original_count', 1), 1)) * 100
            print(f"   Simple RAG: {simple_filtering.get('filtered_count', 0)}/{simple_filtering.get('original_count', 0)} chunks passed threshold ({simple_efficiency:.1f}%)")
            if simple_filtering.get('fallback_used'):
                print(f"   Simple RAG: Fallback applied (minimum chunks requirement)")

        if hier_filtering:
            hier_efficiency = (hier_filtering.get('filtered_count', 0) / max(hier_filtering.get('original_count', 1), 1)) * 100
            print(f"   Hierarchical RAG: {hier_filtering.get('filtered_count', 0)}/{hier_filtering.get('original_count', 0)} chunks passed threshold ({hier_efficiency:.1f}%)")
            if hier_filtering.get('fallback_used'):
                print(f"   Hierarchical RAG: Fallback applied (minimum chunks requirement)")

        # Strategy-specific insights
        print(f"\n💡 STRATEGY-SPECIFIC INSIGHTS:")

        # Hierarchical-specific analysis
        hier_para_indices = set()
        for chunk in hier_chunks:
            if 'paragraph_index' in chunk:
                hier_para_indices.add(chunk['paragraph_index'])

        if hier_para_indices:
            print(f"   Hierarchical RAG used {len(hier_para_indices)} unique paragraphs")
            avg_chunks_per_para = len(hier_chunks) / len(hier_para_indices) if hier_para_indices else 0
            print(f"   Average chunks per paragraph: {avg_chunks_per_para:.1f}")

        print("-" * 120)

    async def run_predefined_tests(self) -> None:
        """Run all predefined test questions and display results"""
        print("\n🧪 RUNNING PREDEFINED TEST QUESTIONS")
        print("=" * 100)
        print(f"Testing {len(self.test_questions)} predefined questions...")

        for i, test_item in enumerate(self.test_questions, 1):
            print(f"\n📋 Test {i}/{len(self.test_questions)}: {test_item['category']} ({test_item['complexity']})")
            print(f"❓ {test_item['question']}")

            try:
                result = await self.compare_all_responses(test_item['question'])

                # Brief summary for batch testing
                no_rag_words = result['no_rag']['word_count']
                simple_words = result['simple_rag']['word_count']
                hier_words = result['hierarchical_rag']['word_count']

                print(f"📊 Summary: No RAG: {no_rag_words}w | Simple: {simple_words}w | Hierarchical: {hier_words}w")

                # Add small delay between tests
                await asyncio.sleep(1)

            except Exception as e:
                print(f"❌ Test failed: {str(e)}")
                continue

        print(f"\n✅ Completed testing {len(self.test_questions)} questions")
        print(f"📈 Total test history: {len(self.test_history)} results")

    async def single_mode_response(self, question: str, mode: str) -> None:
        """Get response from a single specified mode"""
        print(f"\n🤔 Question: {question}")
        print("=" * 80)

        if mode.lower() == 'no-rag':
            print("🔄 Generating No RAG response...")
            result = await self.get_no_rag_response(question)
            title = "🤖 NO RAG RESPONSE"
        elif mode.lower() == 'simple':
            print("🔄 Generating Simple RAG response...")
            result = await self.get_simple_rag_response(question)
            title = "🔍 SIMPLE RAG RESPONSE"
        elif mode.lower() == 'hierarchical':
            print("🔄 Generating Hierarchical RAG response...")
            result = await self.get_hierarchical_rag_response(question)
            title = "🧠 HIERARCHICAL RAG RESPONSE"
        else:
            print(f"❌ Unknown mode: {mode}")
            return

        print(f"\n{title}")
        print(f"⏱️  Time: {result['response_time']:.2f}s | 📝 Words: {result['word_count']}")

        if 'rag' in mode.lower() and result['success']:
            if result.get('rag_context_count', 0) > 0:
                print(f"📚 Found {result['rag_context_count']} relevant chunks")
                if result.get('source_citations'):
                    print(f"📖 Sources: {', '.join(result['source_citations'])}")
                if result.get('rag_scores'):
                    avg_score = sum(result['rag_scores']) / len(result['rag_scores'])
                    print(f"🎯 Avg similarity: {avg_score:.3f}")
            else:
                print("⚠️  No relevant context found")

        print("-" * 80)
        if result['success']:
            print(result['response'])
        else:
            print(f"❌ {result['response']}")

    def show_help(self) -> None:
        """Display help information"""
        self.logger.print("\n📋 COMMAND REFERENCE")
        self.logger.print("=" * 60)
        self.logger.print("🔸 Direct question → Compare all three response modes")
        self.logger.print("🔸 /no-rag <question> → Get No RAG response only")
        self.logger.print("🔸 /simple <question> → Get Simple RAG response only")
        self.logger.print("🔸 /hierarchical <question> → Get Hierarchical RAG response only")
        self.logger.print("🔸 /compare <question> → Compare all three modes (same as direct)")
        self.logger.print("🔸 /test → Run all predefined test questions")
        self.logger.print("🔸 /test-list → Show predefined test questions")
        self.logger.print("🔸 /history → View test history summary")
        self.logger.print("🔸 /clear → Clear test history")
        self.logger.print("🔸 /verbose → Toggle detailed metadata display")
        self.logger.print("🔸 /threshold [show|strategy=value] → Configure similarity thresholds")
        self.logger.print("🔸 /help → Show this help")
        self.logger.print("🔸 /quit or /exit → Exit program")
        self.logger.print("\n💡 EXAMPLE QUESTIONS")
        self.logger.print("• What are the key characteristics of Venus in Gemini?")
        self.logger.print("• What does the 12th house represent in astrology?")
        self.logger.print("• How does a Mars-Jupiter trine aspect influence personality?")
        self.logger.print("• What impression do Scorpio rising people give to others?")
        self.logger.print("\n🎯 TESTING MODES")
        self.logger.print("• No RAG: Baseline GPT-4o responses")
        self.logger.print("• Simple RAG: Uses simple_chunking_strategy namespace")
        self.logger.print("• Hierarchical RAG: Uses hierarchical_chunking_strategy namespace")

    def show_test_list(self) -> None:
        """Display predefined test questions"""
        print("\n📋 PREDEFINED TEST QUESTIONS")
        print("=" * 60)

        for i, test_item in enumerate(self.test_questions, 1):
            print(f"{i:2d}. [{test_item['category']}] ({test_item['complexity']})")
            print(f"    {test_item['question']}")
            print()

    def show_history(self) -> None:
        """Display test history summary"""
        if not self.test_history:
            print("\n📝 No test history available")
            return

        print(f"\n📝 TEST HISTORY SUMMARY ({len(self.test_history)} tests)")
        print("=" * 80)

        for i, test in enumerate(self.test_history[-10:], 1):  # Show last 10 tests
            question = test['question'][:60] + "..." if len(test['question']) > 60 else test['question']

            # Get word counts
            no_rag_words = test['no_rag']['word_count'] if test['no_rag']['success'] else 0
            simple_words = test['simple_rag']['word_count'] if test['simple_rag']['success'] else 0
            hier_words = test['hierarchical_rag']['word_count'] if test['hierarchical_rag']['success'] else 0

            # Get response times
            no_rag_time = test['no_rag']['response_time'] if test['no_rag']['success'] else 0
            simple_time = test['simple_rag']['response_time'] if test['simple_rag']['success'] else 0
            hier_time = test['hierarchical_rag']['response_time'] if test['hierarchical_rag']['success'] else 0

            print(f"{i:2d}. {question}")
            print(f"    Words: No RAG: {no_rag_words:3d} | Simple: {simple_words:3d} | Hierarchical: {hier_words:3d}")
            print(f"    Times: No RAG: {no_rag_time:.2f}s | Simple: {simple_time:.2f}s | Hierarchical: {hier_time:.2f}s")
            print()

    def clear_history(self) -> None:
        """Clear test history"""
        self.test_history.clear()
        print("\n🗑️  Test history cleared")

    def _show_threshold_config(self) -> None:
        """Display current similarity threshold configuration"""
        print("\n🎯 SIMILARITY THRESHOLD CONFIGURATION")
        print("=" * 50)
        print(f"Simple RAG threshold:      {self.similarity_thresholds['simple_rag']:.3f}")
        print(f"Hierarchical RAG threshold: {self.similarity_thresholds['hierarchical_rag']:.3f}")
        print(f"Global default threshold:   {self.similarity_thresholds['global_default']:.3f}")
        print(f"Minimum chunks required:    {self.similarity_thresholds['minimum_chunks']}")
        print("\nUsage:")
        print("  /threshold show                    - Show current settings")
        print("  /threshold simple=0.7              - Set simple RAG threshold")
        print("  /threshold hierarchical=0.6        - Set hierarchical RAG threshold")
        print("  /threshold global=0.65             - Set global default threshold")

    async def run(self) -> None:
        """Run the interactive testing system"""
        self.logger.print("\n🌟 Welcome to Enhanced Astrology RAG Testing System!")
        self.logger.print("💡 Type /help for command reference")
        self.logger.print("🔸 Enter questions directly to compare all three response modes")
        self.logger.print("🔸 Type /quit or /exit to exit the program\n")

        while True:
            try:
                # Get user input
                user_input = input("🤔 Enter your question or command: ").strip()

                if not user_input:
                    continue

                # Handle commands
                if user_input.startswith('/'):
                    await self.handle_command(user_input)
                else:
                    # Direct question → compare all three modes
                    result = await self.compare_all_responses(user_input)
                    self._display_detailed_responses(result)

            except KeyboardInterrupt:
                print("\n👋 Program interrupted, goodbye!")
                break
            except Exception as e:
                print(f"❌ Error during processing: {str(e)}")
                continue

    async def handle_command(self, user_input: str) -> None:
        """Handle command input"""
        command_parts = user_input.split(' ', 1)
        command = command_parts[0].lower()

        if command in ['/quit', '/exit']:
            print("\n👋 Thank you for using the system! Goodbye!")
            sys.exit(0)

        elif command == '/help':
            self.show_help()

        elif command == '/test':
            await self.run_predefined_tests()

        elif command == '/test-list':
            self.show_test_list()

        elif command == '/history':
            self.show_history()

        elif command == '/clear':
            self.clear_history()

        elif command == '/verbose':
            self.verbose_mode = not self.verbose_mode
            status = "enabled" if self.verbose_mode else "disabled"
            print(f"🔍 Verbose mode {status}")

        elif command == '/threshold':
            if len(command_parts) > 1:
                try:
                    threshold_input = command_parts[1].strip()
                    if threshold_input == 'show':
                        self._show_threshold_config()
                    elif '=' in threshold_input:
                        # Set specific threshold: /threshold simple=0.7 or /threshold hierarchical=0.6
                        strategy, value = threshold_input.split('=', 1)
                        threshold_value = float(value)
                        if 0.0 <= threshold_value <= 1.0:
                            if strategy.lower() in ['simple', 'simple_rag']:
                                self.similarity_thresholds['simple_rag'] = threshold_value
                                print(f"🎯 Simple RAG threshold set to {threshold_value:.3f}")
                            elif strategy.lower() in ['hierarchical', 'hierarchical_rag']:
                                self.similarity_thresholds['hierarchical_rag'] = threshold_value
                                print(f"🎯 Hierarchical RAG threshold set to {threshold_value:.3f}")
                            elif strategy.lower() == 'global':
                                self.similarity_thresholds['global_default'] = threshold_value
                                print(f"🎯 Global default threshold set to {threshold_value:.3f}")
                            else:
                                print(f"❌ Unknown strategy: {strategy}. Use 'simple', 'hierarchical', or 'global'")
                        else:
                            print(f"❌ Threshold must be between 0.0 and 1.0")
                    else:
                        print(f"❌ Invalid threshold command. Use '/threshold show' or '/threshold strategy=value'")
                except ValueError:
                    print(f"❌ Invalid threshold value. Must be a number between 0.0 and 1.0")
            else:
                self._show_threshold_config()

        elif command == '/no-rag':
            if len(command_parts) > 1:
                await self.single_mode_response(command_parts[1], 'no-rag')
            else:
                print("❌ Please provide a question, e.g.: /no-rag What is Venus in Gemini?")

        elif command == '/simple':
            if len(command_parts) > 1:
                await self.single_mode_response(command_parts[1], 'simple')
            else:
                print("❌ Please provide a question, e.g.: /simple What is Venus in Gemini?")

        elif command == '/hierarchical':
            if len(command_parts) > 1:
                await self.single_mode_response(command_parts[1], 'hierarchical')
            else:
                print("❌ Please provide a question, e.g.: /hierarchical What is Venus in Gemini?")

        elif command == '/compare':
            if len(command_parts) > 1:
                result = await self.compare_all_responses(command_parts[1])
                self._display_detailed_responses(result)
            else:
                print("❌ Please provide a question, e.g.: /compare What is Venus in Gemini?")

        else:
            print(f"❌ Unknown command: {command}. Type /help for available commands")



async def main():
    """Main function to run the enhanced astrology RAG testing system"""
    try:
        # Ask user about logging preference
        print("🌟 歡迎使用 Enhanced Astrology RAG Testing System")
        print("=" * 60)
        enable_logging = input("是否啟用日誌記錄功能？(y/n，預設為 y): ").strip().lower()
        enable_logging = enable_logging != 'n'  # Default to True unless explicitly 'n'

        # Initialize logger
        logger = RAGLogger(enable_logging=enable_logging)

        if enable_logging:
            logger.print(f"📁 日誌將保存到: {logger.get_log_filename()}")

        # Initialize tester with logger
        tester = AstrologyRAGTester(logger)
        await tester.run()

        # Close logger and show log file location
        if enable_logging:
            log_filename = logger.close()
            if log_filename:
                print(f"\n📁 測試日誌已保存到: {log_filename}")

    except Exception as e:
        print(f"❌ System initialization failed: {str(e)}")
        # Close logger if it exists
        try:
            if 'logger' in locals() and logger.enable_logging:
                log_filename = logger.close()
                if log_filename:
                    print(f"📁 測試日誌已保存到: {log_filename}")
        except:
            pass
        sys.exit(1)


if __name__ == "__main__":
    # Run the enhanced astrology RAG testing system
    asyncio.run(main())

