#!/usr/bin/env python3
"""
占星師 AI 助手 Prompt 生成器

使用方法:
1. 修改 astrologer_prompt_template.json 中的 variables 部分
2. 運行此腳本生成最終的 prompt
3. 或者使用 generate_custom_prompt() 函數動態生成
"""

import json
import re
from typing import Dict, Any, List, Union


class PromptGenerator:
    """Prompt 模板生成器"""
    
    def __init__(self, template_path: str = "astrologer_prompt_template.json"):
        """初始化生成器
        
        Args:
            template_path: JSON 模板文件路徑
        """
        self.template_path = template_path
        self.template_data = self._load_template()
    
    def _load_template(self) -> Dict[str, Any]:
        """載入 JSON 模板"""
        try:
            with open(self.template_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"找不到模板文件: {self.template_path}")
        except json.JSONDecodeError as e:
            raise ValueError(f"JSON 格式錯誤: {e}")
    
    def get_variable_info(self, variable_name: str) -> Dict[str, Any]:
        """獲取變數資訊
        
        Args:
            variable_name: 變數名稱
            
        Returns:
            變數的詳細資訊
        """
        variables = self.template_data.get("variables", {})
        if variable_name not in variables:
            raise KeyError(f"變數 '{variable_name}' 不存在")
        return variables[variable_name]
    
    def list_variables(self) -> List[str]:
        """列出所有可用變數"""
        return list(self.template_data.get("variables", {}).keys())
    
    def get_default_values(self) -> Dict[str, Any]:
        """獲取所有變數的預設值"""
        variables = self.template_data.get("variables", {})
        defaults = {}
        for var_name, var_info in variables.items():
            defaults[var_name] = var_info.get("default")
        return defaults
    
    def _format_list_variable(self, value: List[str]) -> str:
        """格式化列表變數為字串"""
        if not value:
            return ""
        if len(value) == 1:
            return value[0]
        elif len(value) == 2:
            return f"{value[0]}和{value[1]}"
        else:
            return f"{', '.join(value[:-1])}和{value[-1]}"
    
    def _format_array_variable(self, value: List[str]) -> str:
        """格式化陣列變數為編號列表"""
        if not value:
            return ""
        formatted_items = []
        for i, item in enumerate(value, 1):
            formatted_items.append(f"{i}. {item}")
        return "\n".join(formatted_items)
    
    def _format_object_variable(self, value: Dict[str, str], context_key: str = None) -> str:
        """格式化物件變數"""
        if context_key and context_key in value:
            return value[context_key]
        
        # 如果沒有指定 context_key，返回格式化的完整物件
        formatted_items = []
        for key, val in value.items():
            formatted_items.append(f"- **{key}**: {val}")
        return "\n".join(formatted_items)
    
    def _replace_variables(self, template: str, values: Dict[str, Any]) -> str:
        """替換模板中的變數
        
        Args:
            template: 模板字串
            values: 變數值字典
            
        Returns:
            替換後的字串
        """
        def replace_func(match):
            var_expression = match.group(1)
            
            # 處理物件屬性訪問，如 {{tone_description[communication_tone]}}
            if '[' in var_expression and ']' in var_expression:
                obj_match = re.match(r'(\w+)\[(\w+)\]', var_expression)
                if obj_match:
                    obj_name, key_name = obj_match.groups()
                    if obj_name in values and key_name in values:
                        obj_value = values[obj_name]
                        key_value = values[key_name]
                        if isinstance(obj_value, dict) and key_value in obj_value:
                            return obj_value[key_value]
                return f"{{{{未找到變數: {var_expression}}}}}"
            
            # 處理普通變數
            if var_expression in values:
                value = values[var_expression]
                
                # 根據變數類型格式化
                var_info = self.template_data["variables"].get(var_expression, {})
                var_type = var_info.get("type", "text")
                
                if var_type == "multiselect" and isinstance(value, list):
                    return self._format_list_variable(value)
                elif var_type == "array" and isinstance(value, list):
                    return self._format_array_variable(value)
                elif var_type == "object" and isinstance(value, dict):
                    return self._format_object_variable(value)
                else:
                    return str(value)
            
            return f"{{{{未找到變數: {var_expression}}}}}"
        
        # 使用正則表達式替換 {{variable_name}} 格式的變數
        pattern = r'\{\{([^}]+)\}\}'
        return re.sub(pattern, replace_func, template)
    
    def generate_prompt(self, custom_values: Dict[str, Any] = None) -> str:
        """生成最終的 prompt
        
        Args:
            custom_values: 自定義變數值，會覆蓋預設值
            
        Returns:
            生成的 prompt 字串
        """
        # 獲取預設值
        values = self.get_default_values()
        
        # 應用自定義值
        if custom_values:
            values.update(custom_values)
        
        # 獲取模板
        template = self.template_data.get("template", "")
        
        # 替換變數
        return self._replace_variables(template, values)
    
    def save_prompt(self, output_path: str, custom_values: Dict[str, Any] = None) -> None:
        """生成並保存 prompt 到文件
        
        Args:
            output_path: 輸出文件路徑
            custom_values: 自定義變數值
        """
        prompt = self.generate_prompt(custom_values)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(prompt)
        print(f"Prompt 已保存到: {output_path}")


def generate_custom_prompt(**kwargs) -> str:
    """便捷函數：生成自定義 prompt
    
    Args:
        **kwargs: 變數名稱和值的鍵值對
        
    Returns:
        生成的 prompt 字串
        
    Example:
        prompt = generate_custom_prompt(
            communication_tone="理性分析型",
            specialization_areas=["本命盤解讀", "合盤分析"],
            emotional_expression="冷靜客觀"
        )
    """
    generator = PromptGenerator()
    return generator.generate_prompt(kwargs)


def main():
    """主函數：示範用法"""
    generator = PromptGenerator()
    
    # 顯示可用變數
    print("可用變數:")
    for var_name in generator.list_variables():
        var_info = generator.get_variable_info(var_name)
        print(f"- {var_name}: {var_info.get('name', '')} ({var_info.get('type', '')})")
    
    print("\n" + "="*50 + "\n")
    
    # 使用預設值生成 prompt
    print("使用預設值生成的 Prompt:")
    default_prompt = generator.generate_prompt()
    print(default_prompt[:500] + "..." if len(default_prompt) > 500 else default_prompt)
    
    print("\n" + "="*50 + "\n")
    
    # 使用自定義值生成 prompt
    custom_values = {
        "communication_tone": "理性分析型",
        "specialization_areas": ["本命盤解讀", "合盤分析"],
        "emotional_expression": "冷靜客觀"
    }
    
    print("使用自定義值生成的 Prompt:")
    custom_prompt = generator.generate_prompt(custom_values)
    print(custom_prompt[:500] + "..." if len(custom_prompt) > 500 else custom_prompt)
    
    # 保存到文件
    generator.save_prompt("generated_astrologer_prompt.txt", custom_values)


if __name__ == "__main__":
    main()
