#!/usr/bin/env python3
"""
Comprehensive Testing System for Astrologer F-string Prompt Template

This module provides systematic testing of the astrologer prompt generation system
with GPT-4o integration and RAG context retrieval from Pinecone.
"""

import asyncio
import time
import json
import os
from typing import List, Dict, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime
import sys

# Astrologer prompt generation
from prompts.astrologer_fstring_template import (
    create_astrologer_prompt,
    AstrologerStyle,
    AstrologySchool, 
    EmotionalExpression
)

# GPT-4o and RAG integration
from gpt4o_client import GPT4oClient, initialize_gpt4o_client
from pinecone_client import PineconeClient
from config import config


class Logger:
    """Logger class for simultaneous terminal and file output"""

    def __init__(self, enable_logging: bool = True):
        """Initialize logger with optional file logging"""
        self.enable_logging = enable_logging
        self.log_file = None
        self.log_filename = None

        if self.enable_logging:
            self._create_log_file()

    def _create_log_file(self):
        """Create log file with timestamp"""
        # Create logs directory if it doesn't exist
        logs_dir = "logs"
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)

        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_filename = os.path.join(logs_dir, f"astrologer_test_log_{timestamp}.txt")

        try:
            self.log_file = open(self.log_filename, 'w', encoding='utf-8')
            # Write header
            self.log_file.write(f"占星師 AI 測試系統日誌\n")
            self.log_file.write(f"開始時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            self.log_file.write("=" * 60 + "\n\n")
            self.log_file.flush()
        except Exception as e:
            print(f"⚠️  無法創建日誌文件: {e}")
            self.enable_logging = False

    def print(self, *args, **kwargs):
        """Print to both terminal and log file"""
        # Print to terminal
        print(*args, **kwargs)

        # Write to log file if enabled
        if self.enable_logging and self.log_file:
            try:
                # Convert args to string and write to file
                message = ' '.join(str(arg) for arg in args)
                self.log_file.write(message + '\n')
                self.log_file.flush()
            except Exception as e:
                print(f"⚠️  日誌寫入失敗: {e}")

    def log_separator(self, title: str = ""):
        """Add a separator line to the log"""
        separator = "━" * 60
        if title:
            self.print(f"\n{separator}")
            self.print(f"📋 {title}")
            self.print(separator)
        else:
            self.print(separator)

    def log_session_start(self, session_type: str):
        """Log the start of a test session"""
        self.log_separator(f"開始 {session_type}")
        self.print(f"⏰ 時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.print()

    def log_session_end(self, session_type: str):
        """Log the end of a test session"""
        self.log_separator(f"結束 {session_type}")
        self.print(f"⏰ 時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.print()

    def close(self):
        """Close the log file"""
        if self.log_file:
            try:
                self.log_file.write(f"\n結束時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                self.log_file.write("=" * 60 + "\n")
                self.log_file.close()
                return self.log_filename
            except Exception as e:
                print(f"⚠️  關閉日誌文件失敗: {e}")
        return None

    def get_log_filename(self):
        """Get the current log filename"""
        return self.log_filename


@dataclass
class TestConfiguration:
    """Configuration for astrologer testing"""
    name: str
    style: Union[str, AstrologerStyle]
    astrology_school: Union[str, AstrologySchool]
    specialization: Union[str, List[str]]
    emotional_expression: Union[str, EmotionalExpression]
    word_count: int
    interpretation_style: str
    reading_steps: Optional[List[str]] = None
    interaction_methods: Union[str, List[str]] = "引導式提問"
    life_stage_focus: Optional[str] = None
    consultation_approach: str = "溫和而專業"
    include_ethics: bool = True
    include_boundaries: bool = True

    def validate(self) -> bool:
        """Validate configuration parameters"""
        if self.word_count < 200 or self.word_count > 2000:
            return False
        if isinstance(self.style, str) and self.style not in [s.value for s in AstrologerStyle]:
            return False
        return True


class Astrologer:
    """Core astrologer functionality for generating responses"""

    def __init__(self, gpt4o_client: GPT4oClient, pinecone_client: PineconeClient, logger: Logger = None):
        """Initialize the astrologer with required clients"""
        self.gpt4o_client = gpt4o_client
        self.pinecone_client = pinecone_client
        self.logger = logger or Logger(enable_logging=False)  # Default to no logging if not provided

        # Configuration
        self.index_name = config.PINECONE_INDEX_NAME or "astrology-text"
        self.hierarchical_namespace = "hierarchical_chunking_strategy"
        self.simple_namespace = "simple_chunking_strategy"

    async def generate_response(self, config: TestConfiguration, query: str, use_rag: bool = True) -> Dict:
        """Generate astrologer response with given configuration and query"""
        start_time = time.time()

        try:
            # 1. Generate astrologer persona prompt with all parameters
            astrologer_prompt = create_astrologer_prompt(
                style=config.style,
                astrology_school=config.astrology_school,
                specialization=config.specialization,
                emotional_expression=config.emotional_expression,
                word_count=config.word_count,
                interpretation_style=config.interpretation_style,
                reading_steps=config.reading_steps,
                interaction_methods=config.interaction_methods,
                life_stage_focus=config.life_stage_focus,
                consultation_approach=config.consultation_approach,
                include_ethics=config.include_ethics,
                include_boundaries=config.include_boundaries
            )

            # 2. Retrieve RAG context if enabled
            rag_context = None
            rag_info = {"enabled": False, "context_count": 0, "avg_similarity": 0.0}

            if use_rag and self.pinecone_client._pinecone_available:
                try:
                    rag_context = self.pinecone_client.search_rag_context(
                        user_query=query,
                        namespace=self.hierarchical_namespace,
                        top_k=5
                    )

                    if rag_context:
                        rag_info = {
                            "enabled": True,
                            "context_count": len(rag_context),
                            "avg_similarity": sum(ctx.get("score", 0) for ctx in rag_context) / len(rag_context),
                            "sources": [ctx.get("question", "")[:50] + "..." for ctx in rag_context[:3]]
                        }
                except Exception as e:
                    self.logger.print(f"⚠️  RAG 檢索失敗: {str(e)}")
                    rag_context = None

            # 3. Generate response with integrated context
            response = await self.gpt4o_client.generate_response(
                system_prompt=astrologer_prompt,
                user_input=query,
                rag_context=rag_context,
                temperature=0.7,
                max_tokens=config.word_count * 2  # Allow buffer for word count
            )

            # 4. Evaluate response
            evaluation = self.evaluate_response(response, config)

            response_time = time.time() - start_time

            return {
                "success": True,
                "configuration": config.name,
                "query": query,
                "generated_prompt": astrologer_prompt,
                "rag_info": rag_info,
                "ai_response": response,
                "evaluation": evaluation,
                "response_time": response_time,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            response_time = time.time() - start_time
            return {
                "success": False,
                "configuration": config.name,
                "query": query,
                "error": str(e),
                "response_time": response_time,
                "timestamp": datetime.now().isoformat()
            }

    def evaluate_response(self, response: str, config: TestConfiguration) -> Dict:
        """Evaluate response quality against configuration parameters"""
        evaluation = {}

        # Word count accuracy (for Chinese text, count characters excluding spaces and punctuation)
        import re
        # Remove spaces, punctuation, and markdown formatting for more accurate Chinese character count
        clean_text = re.sub(r'[^\u4e00-\u9fff\w]', '', response)
        actual_words = len(clean_text)
        target_words = config.word_count
        word_count_accuracy = min(actual_words / target_words, 1.0) if target_words > 0 else 0

        evaluation["word_count"] = {
            "actual": actual_words,
            "target": target_words,
            "accuracy": word_count_accuracy,
            "status": "good" if 0.8 <= word_count_accuracy <= 1.2 else "needs_adjustment"
        }

        # Response quality metrics
        evaluation["response_quality"] = {
            "length": len(response),
            "has_structure": "###" in response or "**" in response,
            "has_conclusion": any(word in response for word in ["總結", "結論", "建議", "最後"]),
            "professional_tone": True  # Could be enhanced with more sophisticated analysis
        }

        return evaluation


class AstrologerTestSystem:
    """Testing system for astrologer prompt templates"""

    def __init__(self, astrologer: Astrologer = None, logger: Logger = None):
        """Initialize the testing system"""
        # Initialize logger
        self.logger = logger or Logger(enable_logging=False)

        self.logger.print("🌟 初始化占星師 AI 測試系統...")

        # Initialize astrologer instance
        if astrologer is None:
            gpt4o_client = initialize_gpt4o_client()
            pinecone_client = PineconeClient()
            self.astrologer = Astrologer(gpt4o_client, pinecone_client, self.logger)
        else:
            self.astrologer = astrologer
            # Update astrologer's logger if needed
            if hasattr(self.astrologer, 'logger'):
                self.astrologer.logger = self.logger
        
        # Test queries designed to reveal style differences
        self.test_queries = [
            "我的太陽在天蠍座，月亮在雙子座，上升在處女座，這個組合如何影響我的性格？",
            "土星正在經過我的第七宮，這對我的感情關係會有什麼影響？",
            "我是天秤座，我的伴侶是摩羯座，我們的關係相容性如何？",
            "我的火星在天蠍座第八宮，這對我的事業野心有什麼影響？",
            "北交點在雙魚座第十二宮代表什麼靈性課題？",
            "我有太陽刑冥王星的相位，這個困難相位如何轉化為成長力量？",
            "第十二宮在占星學中代表什麼？有什麼重要意義？",
            "火星與木星形成三分相對個性有什麼影響？",
            "自定義問題"  # Placeholder for custom input
        ]
        
        # Preset configurations will be set in main function
        self.preset_configurations = []
        
        self.logger.print(f"✅ 系統初始化完成")
        self.logger.print(f"   - GPT-4o 客戶端: {'可用' if self.astrologer.gpt4o_client else '不可用'}")
        self.logger.print(f"   - Pinecone 客戶端: {'可用' if self.astrologer.pinecone_client._pinecone_available else '不可用'}")
        self.logger.print(f"   - 測試問題數量: {len(self.test_queries)}")
        if self.logger.enable_logging:
            self.logger.print(f"   - 日誌文件: {self.logger.get_log_filename()}")
        self.logger.print()

    def set_configurations(self, configurations: List[TestConfiguration]):
        """Set preset configurations"""
        self.preset_configurations = configurations
        self.logger.print(f"📊 已載入 {len(self.preset_configurations)} 個預設配置")
    




    async def run_single_test(self, config: TestConfiguration, query: str, use_rag: bool = True) -> Dict:
        """Test individual configuration with single query"""
        self.logger.log_session_start("單一配置測試")
        self.logger.print(f"🧪 測試配置: {config.name}")
        self.logger.print(f"❓ 測試問題: {query[:50]}...")

        result = await self.astrologer.generate_response(config, query, use_rag)

        if result["success"]:
            self.logger.print(f"✅ 測試完成 - 響應時間: {result['response_time']:.2f}秒")
            self._display_single_result(result)
        else:
            self.logger.print(f"❌ 測試失敗: {result.get('error', 'Unknown error')}")

        self.logger.log_session_end("單一配置測試")
        return result



    def _display_single_result(self, result: Dict) -> None:
        """Display detailed result for single test"""
        self.logger.print(f"\n📝 生成的 Prompt 預覽:")
        self.logger.print(f"   {result['generated_prompt'][:200]}...")

        rag_info = result.get("rag_info", {})
        if rag_info.get("enabled"):
            self.logger.print(f"\n🔍 RAG 檢索結果: 找到 {rag_info['context_count']} 個相關資料片段 "
                  f"(平均相似度: {rag_info['avg_similarity']:.3f})")
        else:
            self.logger.print(f"\n🔍 RAG 檢索: 未啟用或無可用資料")

        self.logger.print(f"\n🤖 AI 回應:")
        self.logger.print(f"{result['ai_response']}")

        eval_data = result["evaluation"]
        self.logger.print(f"\n📊 評估結果:")
        self.logger.print(f"   ✅ 字數: {eval_data['word_count']['actual']}/{eval_data['word_count']['target']} "
              f"(準確率: {eval_data['word_count']['accuracy']:.1%})")
        self.logger.print(f"   ✅ 回應品質: 長度 {eval_data['response_quality']['length']} 字符")
        self.logger.print(f"   ✅ 結構化: {'是' if eval_data['response_quality']['has_structure'] else '否'}")
        self.logger.print(f"   ✅ 有結論: {'是' if eval_data['response_quality']['has_conclusion'] else '否'}")

        self.logger.print(f"\n⏱️  響應時間: {result['response_time']:.2f}秒")
        self.logger.print("━" * 60)



    def export_results(self, results: Dict, filename: str = None, format: str = "json") -> str:
        """Export test results to file"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"astrologer_test_results_{timestamp}.{format}"

        try:
            if format == "json":
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(results, f, ensure_ascii=False, indent=2)
            else:
                raise ValueError(f"Unsupported format: {format}")

            print(f"📁 結果已導出到: {filename}")
            return filename

        except Exception as e:
            print(f"❌ 導出失敗: {str(e)}")
            return ""

    def show_menu(self) -> None:
        """Display main menu"""
        self.logger.print("\n🌟 占星師 AI 測試系統")
        self.logger.print("━" * 60)
        self.logger.print("請選擇測試模式:")
        self.logger.print("1. 🎯 測試單一配置")
        self.logger.print("2. ❌ 退出")
        self.logger.print("━" * 60)

    def show_configurations(self) -> None:
        """Display preset configurations"""
        self.logger.print("\n📊 預設配置列表:")
        self.logger.print("━" * 60)

        for i, config in enumerate(self.preset_configurations, 1):
            self.logger.print(f"{i}. {config.name}")
            self.logger.print(f"   風格: {config.style.value if isinstance(config.style, AstrologerStyle) else config.style}")
            self.logger.print(f"   學派: {config.astrology_school.value if isinstance(config.astrology_school, AstrologySchool) else config.astrology_school}")
            self.logger.print(f"   專精: {config.specialization}")
            self.logger.print(f"   字數: {config.word_count}")
            self.logger.print()

    def show_test_queries(self) -> None:
        """Display test queries"""
        self.logger.print("\n📋 測試問題列表:")
        self.logger.print("━" * 60)

        for i, query in enumerate(self.test_queries, 1):
            if i == 9:  # Custom input option
                self.logger.print(f"{i}. 我想自己輸入問題")
            else:
                self.logger.print(f"{i}. {query}")
        self.logger.print()

    async def interactive_menu(self) -> None:
        """Interactive menu system"""
        while True:
            self.show_menu()

            try:
                choice = input("請輸入選項 (1 或 2): ").strip()

                if choice == "1":
                    self.show_configurations()
                    config_idx = int(input("選擇配置 (1-4): ")) - 1

                    if 0 <= config_idx < len(self.preset_configurations):
                        self.show_test_queries()
                        query_idx = int(input("選擇問題 (1-9): ")) - 1

                        if 0 <= query_idx < len(self.test_queries):
                            config = self.preset_configurations[config_idx]

                            # Handle custom input
                            if query_idx == 8:  # Index 8 is the 9th option (custom input)
                                self.logger.print("\n📝 請輸入您的占星問題:")
                                custom_query = input("問題: ").strip()
                                if custom_query:
                                    query = custom_query
                                    self.logger.print(f"\n✅ 您的問題: {query}")
                                else:
                                    self.logger.print("❌ 問題不能為空")
                                    continue
                            else:
                                query = self.test_queries[query_idx]

                            await self.run_single_test(config, query)
                        else:
                            self.logger.print("❌ 無效的問題編號")
                    else:
                        self.logger.print("❌ 無效的配置編號")

                elif choice == "2":
                    self.logger.print("👋 感謝使用占星師 AI 測試系統!")
                    break

                else:
                    self.logger.print("❌ 無效選項，請選擇 1 或 2")

                input("\n按 Enter 繼續...")

            except (ValueError, KeyboardInterrupt):
                self.logger.print("\n👋 程序已退出")
                break
            except Exception as e:
                self.logger.print(f"❌ 發生錯誤: {str(e)}")
                input("按 Enter 繼續...")


async def main():
    """Main function"""
    try:
        # Ask user about logging preference
        print("🌟 歡迎使用占星師 AI 測試系統")
        print("━" * 60)
        enable_logging = input("是否啟用日誌記錄功能？(y/n，預設為 y): ").strip().lower()
        enable_logging = enable_logging != 'n'  # Default to True unless explicitly 'n'

        # Initialize logger
        logger = Logger(enable_logging=enable_logging)

        if enable_logging:
            logger.print(f"📁 日誌將保存到: {logger.get_log_filename()}")

        # Initialize clients
        gpt4o_client = initialize_gpt4o_client()
        pinecone_client = PineconeClient()

        # Create astrologer instance
        astrologer = Astrologer(gpt4o_client, pinecone_client, logger)

        # Initialize test system with astrologer and logger
        test_system = AstrologerTestSystem(astrologer, logger)

        # Set up preset configurations with complete parameters
        preset_configurations = [
            TestConfiguration(
                name="溫暖療癒師",
                style=AstrologerStyle.HEALING,
                astrology_school=AstrologySchool.PSYCHOLOGICAL,
                specialization=["本命盤解讀", "情感療癒"],
                emotional_expression=EmotionalExpression.WARM_EMPATHY,
                word_count=600,
                interpretation_style="溫暖而深入的分析",
                reading_steps=[
                    "建立溫暖的連結，了解來訪者的情感需求",
                    "觀察星盤整體能量分佈，感受主要的情感主題",
                    "分析太陽、月亮、上升的核心人格特質",
                    "解讀各行星在星座和宮位的情感表現",
                    "分析重要相位關係，特別關注內在衝突與和諧",
                    "整合資訊，提供溫暖的成長建議和情感支持"
                ],
                interaction_methods=["引導式提問", "情感支持"],
                life_stage_focus="青年期",
                consultation_approach="溫暖包容，重視情感療癒",
                include_ethics=True,
                include_boundaries=True
            ),

            TestConfiguration(
                name="學術分析師",
                style=AstrologerStyle.ANALYTICAL,
                astrology_school=AstrologySchool.CLASSICAL,
                specialization="古典占星技法研究",
                emotional_expression=EmotionalExpression.CALM_OBJECTIVE,
                word_count=800,
                interpretation_style="邏輯清晰且客觀的分析",
                reading_steps=[
                    "系統性檢視星盤的基本結構和格局",
                    "運用古典占星技法分析行星力量和尊貴",
                    "詳細解讀各行星在星座的本質表現",
                    "分析宮位系統和行星宮位關係",
                    "運用相位理論進行精確的影響力分析",
                    "整合古典技法，提供結構化的專業解讀"
                ],
                interaction_methods=["主動解讀", "學術討論"],
                life_stage_focus=None,
                consultation_approach="客觀專業，重視技法準確性",
                include_ethics=True,
                include_boundaries=True
            ),

            TestConfiguration(
                name="靈性導師",
                style=AstrologerStyle.MYSTICAL,
                astrology_school=AstrologySchool.EVOLUTIONARY,
                specialization=["靈魂成長", "生命課題探索"],
                emotional_expression=EmotionalExpression.ENCOURAGING,
                word_count=700,
                interpretation_style="富有詩意且具靈性深度",
                reading_steps=[
                    "連結宇宙能量，感受靈魂的呼喚",
                    "探索南北交點揭示的靈魂課題",
                    "分析冥王星、海王星、天王星的世代使命",
                    "解讀個人行星的靈性成長潛能",
                    "識別業力模式和轉化機會",
                    "提供靈性成長的指引和覺醒建議"
                ],
                interaction_methods=["引導式提問", "靈性啟發"],
                life_stage_focus="中年期",
                consultation_approach="神秘智慧，重視靈性覺醒",
                include_ethics=True,
                include_boundaries=True
            ),

            TestConfiguration(
                name="實用顧問",
                style=AstrologerStyle.DIRECT,
                astrology_school=AstrologySchool.WESTERN,
                specialization=["事業發展", "實用建議"],
                emotional_expression=EmotionalExpression.CALM_OBJECTIVE,
                word_count=500,
                interpretation_style="直接且實用",
                reading_steps=[
                    "快速識別關鍵問題和核心需求",
                    "分析第十宮和火星的事業潛能",
                    "解讀金星和第二宮的財務狀況",
                    "評估當前流年對實際生活的影響",
                    "提供具體可行的行動建議",
                    "總結重點，給出明確的實用指導"
                ],
                interaction_methods=["回應式諮詢", "直接建議"],
                life_stage_focus=None,
                consultation_approach="直接高效，重視實用價值",
                include_ethics=True,
                include_boundaries=True
            )
        ]

        # Set configurations
        test_system.set_configurations(preset_configurations)

        # Start interactive menu
        await test_system.interactive_menu()

        # Close logger and show log file location
        if enable_logging:
            log_filename = logger.close()
            if log_filename:
                print(f"\n📁 測試日誌已保存到: {log_filename}")

    except KeyboardInterrupt:
        print("\n👋 程序已退出")
        # Close logger if it exists
        try:
            if 'logger' in locals() and logger.enable_logging:
                log_filename = logger.close()
                if log_filename:
                    print(f"📁 測試日誌已保存到: {log_filename}")
        except:
            pass
    except Exception as e:
        print(f"❌ 系統錯誤: {str(e)}")
        # Close logger if it exists
        try:
            if 'logger' in locals() and logger.enable_logging:
                log_filename = logger.close()
                if log_filename:
                    print(f"📁 測試日誌已保存到: {log_filename}")
        except:
            pass


if __name__ == "__main__":
    asyncio.run(main())
