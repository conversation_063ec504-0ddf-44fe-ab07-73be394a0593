"""
Configuration management for the enhanced API server.
Loads environment variables and provides centralized configuration access.
"""

import os
from typing import Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """Centralized configuration management class."""
    
    # Azure OpenAI Configuration
    AZURE_OPENAI_ENDPOINT: str = os.getenv("AZURE_OPENAI_ENDPOINT", "")
    AZURE_OPENAI_API_KEY: str = os.getenv("AZURE_OPENAI_API_KEY", "")
    AZURE_OPENAI_API_VERSION: str = os.getenv("AZURE_OPENAI_API_VERSION", "2024-08-01-preview")
    AZURE_OPENAI_DEPLOYMENT_NAME: str = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME", "gpt-4o")
    AZURE_OPENAI_EMBEDDING_DEPLOYMENT: str = os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT", "text-embedding-3-small")
    
    # Legacy Azure API Configuration (for backward compatibility)
    AZURE_API_END: str = os.getenv("AZURE_API_END", "")
    AZURE_API_KEY: str = os.getenv("AZURE_API_KEY", "")
    EMBED_KEY: str = os.getenv("EMBED_KEY", "")
    EMBED_END: str = os.getenv("EMBED_END", "")
    
    # Pinecone Configuration
    PINECONE_API_KEY: str = os.getenv("PINECONE_API_KEY", "")
    PINECONE_ENVIRONMENT: str = os.getenv("PINECONE_ENVIRONMENT", "")
    
    # Model Paths
    VI_MODEL_PATH: str = os.getenv("VI_MODEL_PATH", "/Users/<USER>/workspace/gis_quant/Qwen14b_VI_mlx_4bit")
    LAW_MODEL_PATH: str = os.getenv("LAW_MODEL_PATH", "/Users/<USER>/workspace/t1_api_server/T1_law_0610")
    FINANCE_MODEL_PATH: str = os.getenv("FINANCE_MODEL_PATH", "/Users/<USER>/workspace/t1_api_server/qwen3_4b_finance_pre_rl_sft_5_mlx")
    TOOL_MODEL_PATH: str = os.getenv("TOOL_MODEL_PATH", "/Users/<USER>/workspace/t1_api_server/mistral_tool_call_1_mlx_4bit")
    
    # Server Configuration
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", "3001"))
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"
    
    # RAG Configuration
    PINECONE_INDEX_NAME: str = os.getenv("PINECONE_INDEX_NAME", "astrology-text")
    PINECONE_NAMESPACE: str = os.getenv("PINECONE_NAMESPACE", "astrology-text")
    RAG_TOP_K: int = int(os.getenv("RAG_TOP_K", "5"))
    
    # Application Configuration
    RESPONSE_TIMEOUT: int = 3600  # 1 hour
    REQUEST_TIMEOUT: int = 3600   # 1 hour
    MAX_CONTENT_LENGTH: int = 16 * 1024 * 1024  # 16MB
    SEND_FILE_MAX_AGE_DEFAULT: int = 3600  # 1 hour
    PERMANENT_SESSION_LIFETIME: int = 3600  # 1 hour
    
    @classmethod
    def validate_config(cls) -> bool:
        """Validate that required configuration values are present."""
        required_fields = [
            "AZURE_OPENAI_ENDPOINT",
            "AZURE_OPENAI_API_KEY",
            "VI_MODEL_PATH"
        ]
        
        missing_fields = []
        for field in required_fields:
            if not getattr(cls, field):
                missing_fields.append(field)
        
        if missing_fields:
            print(f"Missing required configuration fields: {', '.join(missing_fields)}")
            return False
        
        return True
    
    @classmethod
    def get_azure_openai_config(cls) -> dict:
        """Get Azure OpenAI configuration as a dictionary."""
        return {
            "azure_endpoint": cls.AZURE_OPENAI_ENDPOINT,
            "api_key": cls.AZURE_OPENAI_API_KEY,
            "api_version": cls.AZURE_OPENAI_API_VERSION,
            "azure_deployment": cls.AZURE_OPENAI_DEPLOYMENT_NAME
        }
    
    @classmethod
    def get_pinecone_config(cls) -> dict:
        """Get Pinecone configuration as a dictionary."""
        return {
            "api_key": cls.PINECONE_API_KEY,
            "environment": cls.PINECONE_ENVIRONMENT,
            "index_name": cls.PINECONE_INDEX_NAME,
            "namespace": cls.PINECONE_NAMESPACE,
            "top_k": cls.RAG_TOP_K
        }

# Create a global config instance
config = Config()

# Validate configuration on import
if not config.validate_config():
    print("Warning: Configuration validation failed. Some features may not work correctly.")
