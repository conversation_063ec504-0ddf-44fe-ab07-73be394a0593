import{s as c,r as h,aI as S,L as E,C as p,j as i,aH as y,l as W}from"./index.CbQtRkVt.js";import{w as b,E as v}from"./withFullScreenWrapper.DWXejOhQ.js";import{S as u,T}from"./Toolbar.D6yqQ65-.js";const F=c("div",{target:"evl31sl0"})(({theme:e})=>({display:"flex",flexDirection:"row",flexWrap:"wrap",rowGap:e.spacing.lg,maxWidth:"100%",width:"fit-content"})),j=c("div",{target:"evl31sl1"})(({theme:e})=>({display:"flex",flexDirection:"column",alignItems:"stretch",width:"auto",flexGrow:0,">img":{borderRadius:e.radii.default}})),H=c("div",{target:"evl31sl2"})(({theme:e})=>({textAlign:"center",marginTop:e.spacing.xs,wordWrap:"break-word",padding:e.spacing.threeXS})),M=W.getLogger("ImageList");function G({element:e,endpoints:g,disableFullscreenMode:x}){const{expanded:s,width:f,height:n,expand:w,collapse:C}=S(v),{libConfig:I}=h.useContext(E),m=f||0;let o;const r=e.width;if([-1,-3,-4].includes(r))o=void 0;else if([-2,-5].includes(r))o=m;else if(r>0)o=r;else throw Error(`Invalid image width: ${r}`);const t={};n&&s?(t.maxHeight=n,t.objectFit="contain",t.width="100%"):(t.width=o??"100%",t.maxWidth="100%");const L=l=>{const a=l.currentTarget.src;M.error(`Client Error: Image source error - ${a}`),g.sendClientErrorToHost("Image","Image source failed to load","onerror triggered",a)};return p(u,{width:m,height:n,useContainerWidth:s,topCentered:!0,children:[i(T,{target:u,isFullScreen:s,onExpand:w,onCollapse:C,disableFullscreenMode:x}),i(F,{className:"stImage","data-testid":"stImage",children:e.imgs.map((l,a)=>{const d=l;return p(j,{"data-testid":"stImageContainer",children:[i("img",{style:t,src:g.buildMediaURL(d.url),alt:a.toString(),onError:L,crossOrigin:I.resourceCrossOriginMode}),d.caption&&i(H,{"data-testid":"stImageCaption",style:t,children:i(y,{source:d.caption,allowHTML:!1,isCaption:!0,isLabel:!0})})]},a)})})]})}const O=b(G),D=h.memo(O);export{D as default};
