import{r as l,E as _,_ as G,s as p,B as dt,z as M,j as o,b9 as Y,bu as ct,C as S,bp as J,b4 as q,bo as E,a_ as pt,bI as ut,bG as j,bj as gt,bJ as v,bs as ht,F as Q,aP as ft,bg as mt,bK as yt,N as H,bl as Ct,bk as bt}from"./index.CbQtRkVt.js";import{g as vt,C as It,I as xt,F as $,E as wt,a as St,s as Ft,u as Ut,b as zt}from"./FileHelper.D0K06YBq.js";import{I as Et}from"./InputInstructions.D3IDU-eY.js";import{i as Tt}from"./inputUtils.CptNuJwn.js";import{u as Bt,T as Dt}from"./useTextInputAutoExpand.BuE9l5TG.js";import{E as Rt}from"./ErrorOutline.esm.BEZPMjuG.js";import{U as Z}from"./UploadFileInfo.C-jY39rj.js";import"./base-input.DMlw5p7n.js";var tt=l.forwardRef(function(t,e){var r={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return l.createElement(_,G({iconAttrs:r,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},t,{ref:e}),l.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),l.createElement("path",{d:"M16.5 6v11.5c0 2.21-1.79 4-4 4s-4-1.79-4-4V5a2.5 2.5 0 015 0v10.5c0 .55-.45 1-1 1s-1-.45-1-1V6H10v9.5a2.5 2.5 0 005 0V5c0-2.21-1.79-4-4-4S7 2.79 7 5v12.5c0 3.04 2.46 5.5 5.5 5.5s5.5-2.46 5.5-5.5V6h-1.5z"}))});tt.displayName="AttachFile";var et=l.forwardRef(function(t,e){var r={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return l.createElement(_,G({iconAttrs:r,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},t,{ref:e}),l.createElement("rect",{width:24,height:24,fill:"none"}),l.createElement("path",{d:"M3 5.51v3.71c0 .46.31.86.76.97L11 12l-7.24 1.81c-.45.11-.76.51-.76.97v3.71c0 .72.73 1.2 1.39.92l15.42-6.49c.82-.34.82-1.5 0-1.84L4.39 4.58C3.73 4.31 3 4.79 3 5.51z"}))});et.displayName="Send";const Ht=p("div",{target:"e1togvvn0"})("border:none;position:relative;display:flex;"),Lt=p("div",{target:"e1togvvn1"})(({theme:t,extended:e})=>({border:`${t.sizes.borderWidth} solid`,borderColor:t.colors.widgetBorderColor??t.colors.transparent,borderRadius:t.radii.chatInput,backgroundColor:t.colors.secondaryBg,position:"relative",flexGrow:1,display:"flex",alignItems:"center",paddingLeft:t.spacing.lg,maxHeight:e?"none":t.sizes.minElementHeight,gap:t.spacing.sm,overflow:"hidden",":focus-within":{borderColor:t.colors.primary}})),kt=p("button",{target:"e1togvvn2"})(({theme:t,disabled:e,extended:r})=>{const d=dt(t),[f,c]=d?[t.colors.gray60,t.colors.gray80]:[t.colors.gray80,t.colors.gray40];return{border:"none",backgroundColor:t.colors.transparent,borderTopRightRadius:r?"0":t.radii.chatInput,borderTopLeftRadius:r?t.radii.default:"0",borderBottomRightRadius:t.radii.chatInput,display:"inline-flex",alignItems:"center",justifyContent:"center",lineHeight:t.lineHeights.none,margin:t.spacing.none,padding:t.spacing.sm,color:e?f:c,pointerEvents:"auto","&:focus":{outline:"none"},":focus":{outline:"none"},"&:focus-visible":{backgroundColor:d?t.colors.gray10:t.colors.gray90},"&:hover":{color:t.colors.primary},"&:disabled, &:disabled:hover, &:disabled:active":{backgroundColor:t.colors.transparent,borderColor:t.colors.transparent,color:t.colors.gray,cursor:"not-allowed"}}}),$t=p("div",{target:"e1togvvn3"})(({theme:t})=>({display:"flex",alignItems:"flex-end",height:"100%",position:"absolute",right:0,marginBottom:`-${t.sizes.borderWidth}`,pointerEvents:"none"})),Mt=p("div",{target:"e1togvvn4"})(({theme:t})=>({position:"absolute",bottom:"0px",right:`calc(${t.iconSizes.xl} + 2 * ${t.spacing.sm} + ${t.spacing.sm})`})),Vt=p("div",{target:"e15560op0"})(({theme:t,height:e})=>({backgroundColor:t.colors.transparent,position:"absolute",left:0,bottom:0,minHeight:`max(${t.sizes.emptyDropdownHeight}, ${e})`,width:"100%",zIndex:t.zIndices.priority})),Wt=p("div",{target:"e15560op1"})(({theme:t,height:e})=>({border:`${t.sizes.borderWidth} solid`,borderColor:t.colors.primary,borderRadius:t.radii.chatInput,backgroundColor:t.colors.secondaryBg,color:t.colors.primary,display:"flex",alignItems:"center",justifyContent:"center",height:e,width:"100%",fontWeight:t.fontWeights.bold})),Nt=p("div",{target:"e15560op2"})(({theme:t,disabled:e})=>({display:"flex",alignItems:"top",height:"100%",marginTop:`-${t.sizes.borderWidth}`,cursor:e?"not-allowed":"auto"})),At=p("div",{target:"e15560op3"})(({disabled:t})=>({pointerEvents:t?"none":"auto"})),Pt=p("div",{target:"e15560op4"})(({theme:t})=>({marginTop:"0.625em",marginLeft:t.spacing.sm,height:t.spacing.xl,width:t.sizes.borderWidth,backgroundColor:t.colors.fadedText20})),Kt=p("div",{target:"e15560op5"})(({theme:t})=>({left:0,right:0,lineHeight:t.lineHeights.tight,paddingLeft:t.spacing.sm,paddingRight:t.spacing.sm,overflowX:"auto"})),Ot=p("div",{target:"e15560op6"})({display:"flex"}),Xt=p("div",{target:"e15560op7"})({flex:"0 0 auto"}),jt=p("div",{target:"e15560op8"})(({theme:t})=>({display:"flex",alignItems:"center",padding:t.spacing.sm,gap:t.spacing.twoXS})),_t=p("div",{target:"e15560op9"})(({theme:t})=>({color:t.colors.fadedText60})),Gt=p("div",{target:"e15560op10"})(({theme:t,fileStatus:e})=>({overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",color:e.type==="uploaded"?t.colors.bodyText:t.colors.fadedText60})),Yt=p("div",{target:"e15560op11"})(({theme:t})=>({marginRight:t.spacing.md,color:t.colors.fadedText60})),Jt=p("small",{target:"e15560op12"})(({theme:t})=>({display:"flex",alignItems:"center",maxHeight:t.sizes.smallElementHeight,color:t.colors.fadedText60,"& :hover":{color:t.colors.bodyText}}));function qt({children:t,content:e}){const r=M();return o(ct,{content:e,placement:Y.TOP,overrides:{Body:{style:{top:`-${r.sizes.minElementHeight}`}}},children:t})}const Qt=({fileInfo:t})=>{const e=M(),{type:r}=t.status;switch(r){case"uploading":return o(ut,{usingCustomTheme:!1,"data-testid":"stChatInputFileIconSpinner",size:"lg",margin:"0",padding:"0"});case"error":return o(qt,{content:t.status.errorMessage,children:o(E,{color:e.colors.red,content:Rt,size:"lg"})});case"uploaded":return o(E,{content:xt,size:"lg"});default:return pt(r),null}},Zt=({fileInfo:t,onDelete:e})=>S(jt,{className:"stChatInputFile","data-testid":"stChatInputFile",children:[o(_t,{children:o(Qt,{fileInfo:t})}),o(Gt,{className:"stChatInputFileName","data-testid":"stChatInputFileName",title:t.name,fileStatus:t.status,children:t.name}),o(Yt,{children:vt(t.size,$.Byte)}),o(Jt,{"data-testid":"stChatInputDeleteBtn",children:o(J,{onClick:()=>e(t.id),kind:q.MINIMAL,children:o(E,{content:It,size:"lg"})})})]}),te=l.memo(Zt),ee=({items:t,onDelete:e})=>o(Kt,{"data-testid":"stChatUploadedFiles",children:o(Ot,{children:t.map(r=>o(Xt,{children:o(te,{fileInfo:r,onDelete:e})},r.id))})}),oe=l.memo(ee),ne=({getNextLocalFileId:t,addFiles:e,updateFile:r,uploadClient:d,element:f,onUploadProgress:c,onUploadComplete:C})=>(y,m)=>{const g=j.CancelToken.source(),u=new Z(m.name,m.size,t(),{type:"uploading",cancelToken:g,progress:1});e([u]),d.uploadFile({formId:"",...f},y.uploadUrl,m,h=>c(h,u.id),g.token).then(()=>C(u.id,y)).catch(h=>{j.isCancel(h)||r(u.id,u.setStatus({type:"error",errorMessage:h?h.toString():"Unknown error"}))})},ae=({acceptMultipleFiles:t,maxFileSize:e,uploadClient:r,uploadFile:d,addFiles:f,getNextLocalFileId:c,deleteExistingFiles:C,onUploadComplete:y})=>(m,g)=>{if(!t&&m.length===0&&g.length>1){const u=g.findIndex(h=>h.errors?.[0].code===wt.TooManyFiles);u>=0&&(m.push(g[u].file),g.splice(u,1))}if(!t&&m.length>0&&C(),r.fetchFileURLs(m).then(u=>{gt(u,m).forEach(([h,T])=>{d(h,T)})}).catch(u=>{f(m.map(h=>new Z(h.name,h.size,c(),{type:"error",errorMessage:u})))}),g.length>0){const u=g.map(h=>St(h,c(),e));f(u)}y()},re=({getRootProps:t,getInputProps:e,acceptFile:r,disabled:d,theme:f})=>S(Nt,{disabled:d,children:[S(At,{"data-testid":"stChatInputFileUploadButton",disabled:d,...t(),children:[o("input",{...e()}),o(ht,{content:`Upload or drag and drop ${r===v.Multiple?"files":"a file"}`,placement:Y.TOP,onMouseEnterDelay:500,children:o(J,{kind:q.MINIMAL,disabled:d,children:o(E,{content:tt,size:"lg",color:d?f.colors.fadedText40:f.colors.fadedText60})})})]}),o(Pt,{})]}),ie=l.memo(re),se=({getRootProps:t,getInputProps:e,acceptFile:r,inputHeight:d})=>S(Q,{children:[o(Vt,{height:d,...t(),children:o("input",{...e()})}),o(Wt,{height:d,children:`Drag and drop ${r===v.Multiple?"files":"a file"} here`})]}),le=l.memo(se),L=(t,e,r)=>r.map(d=>d.id===t?e:d),k=(t,e)=>e.find(r=>r.id===t);function de({disabled:t,element:e,widgetMgr:r,fragmentId:d,uploadClient:f}){const c=M(),{placeholder:C,maxChars:y}=e,m=l.useRef(0),g=l.useRef(null),[u,h]=ft(),{innerWidth:T,innerHeight:V}=mt(),[F,B]=l.useState(e.default),[I,x]=l.useState([]),[U,D]=l.useState(!1),w=Bt({textareaRef:g,dependencies:[C]}),R=l.useMemo(()=>I.some(n=>n.status.type==="uploading")?!1:F!==""||I.length>0,[I,F]),b=yt(e.acceptFile),W=Ft(e.maxUploadSizeMb,$.Megabyte,$.Byte),N=l.useCallback(n=>x(i=>[...i,...n]),[]),A=l.useCallback(n=>{x(i=>{const s=k(n,i);return H(s)?i:(s.status.type==="uploading"&&s.status.cancelToken.cancel(),s.status.type==="uploaded"&&s.status.fileUrls.deleteUrl&&f.deleteFile(s.status.fileUrls.deleteUrl),i.filter(a=>a.id!==n))})},[f]),ot=()=>{const n=I.filter(i=>i.status.type==="uploaded").map(i=>{const{name:s,size:a,status:z}=i,{fileId:st,fileUrls:lt}=z;return new Ct({fileId:st,fileUrls:lt,name:s,size:a})});return new bt({uploadedFileInfo:n})},P=()=>m.current++,nt=ae({acceptMultipleFiles:b===v.Multiple,maxFileSize:W,uploadClient:f,uploadFile:ne({getNextLocalFileId:P,addFiles:N,updateFile:(n,i)=>{x(s=>L(n,i,s))},uploadClient:f,element:e,onUploadProgress:(n,i)=>{x(s=>{const a=k(i,s);if(H(a)||a.status.type!=="uploading")return s;const z=Math.round(n.loaded*100/n.total);return a.status.progress===z?s:L(i,a.setStatus({type:"uploading",cancelToken:a.status.cancelToken,progress:z}),s)})},onUploadComplete:(n,i)=>{x(s=>{const a=k(n,s);return H(a)||a.status.type!=="uploading"?s:L(a.id,a.setStatus({type:"uploaded",fileId:i.fileId,fileUrls:i}),s)})}}),addFiles:N,getNextLocalFileId:P,deleteExistingFiles:()=>I.forEach(n=>A(n.id)),onUploadComplete:()=>{g.current&&g.current.focus()}}),{getRootProps:K,getInputProps:O}=Ut({onDrop:nt,multiple:b===v.Multiple,accept:zt(e.fileType),maxSize:W}),X=()=>{if(g.current&&g.current.focus(),!R||t)return;const n={data:F,fileUploaderState:ot()};r.setChatInputValue(e,n,{fromUi:!0},d),x([]),B("")},at=n=>{const{metaKey:i,ctrlKey:s,shiftKey:a}=n;Tt(n)&&!a&&!s&&!i&&(n.preventDefault(),X())},rt=n=>{const{value:i}=n.target;y!==0&&i.length>y||(B(i),w.updateScrollHeight())};l.useEffect(()=>{if(e.setValue){e.setValue=!1;const n=e.value||"";B(n)}},[e]),l.useEffect(()=>{const n=a=>{a.preventDefault(),a.stopPropagation(),!U&&a.dataTransfer?.types.includes("Files")&&D(!0)},i=a=>{a.preventDefault(),a.stopPropagation(),U&&(a.clientX<=0&&a.clientY<=0||a.clientX>=T&&a.clientY>=V)&&D(!1)},s=a=>{a.preventDefault(),a.stopPropagation(),U&&D(!1)};return window.addEventListener("dragover",n),window.addEventListener("drop",s),window.addEventListener("dragleave",i),()=>{window.removeEventListener("dragover",n),window.removeEventListener("drop",s),window.removeEventListener("dragleave",i)}},[U,T,V]);const it=b!==v.None&&U;return S(Q,{children:[b===v.None?null:o(oe,{items:[...I],onDelete:A}),o(Ht,{className:"stChatInput","data-testid":"stChatInput",ref:h,children:it?o(le,{getRootProps:K,getInputProps:O,acceptFile:b,inputHeight:w.height}):S(Lt,{extended:w.isExtended,children:[b===v.None?null:o(ie,{getRootProps:K,getInputProps:O,acceptFile:b,disabled:t,theme:c}),o(Dt,{inputRef:g,value:F,placeholder:C,onChange:rt,onKeyDown:at,"aria-label":C,disabled:t,rows:1,overrides:{Root:{style:{minHeight:c.sizes.minElementHeight,outline:"none",borderLeftWidth:"0",borderRightWidth:"0",borderTopWidth:"0",borderBottomWidth:"0",borderTopLeftRadius:"0",borderTopRightRadius:"0",borderBottomRightRadius:"0",borderBottomLeftRadius:"0"}},Input:{props:{"data-testid":"stChatInputTextArea"},style:{fontWeight:c.fontWeights.normal,lineHeight:c.lineHeights.inputWidget,"::placeholder":{color:c.colors.fadedText60},height:w.height,maxHeight:w.maxHeight,paddingLeft:c.spacing.none,paddingBottom:c.spacing.sm,paddingTop:c.spacing.sm,paddingRight:`calc(${c.iconSizes.xl} + 2 * ${c.spacing.sm} + ${c.spacing.sm})`}}}}),u>c.breakpoints.hideWidgetDetails&&o(Mt,{children:o(Et,{dirty:R,value:F,maxLength:y,type:"chat",inForm:!1})}),o($t,{children:o(kt,{onClick:X,disabled:!R||t,extended:w.isExtended,"data-testid":"stChatInputSubmitButton",children:o(E,{content:et,size:"xl",color:"inherit"})})})]})})]})}const Ce=l.memo(de);export{Ce as default};
