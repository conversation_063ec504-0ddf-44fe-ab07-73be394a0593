"""
Astrologer configuration loader for reading and validating astrologer.json.
Provides structured access to astrologer persona and behavior settings.
"""

import json
import os
from typing import Dict, Any, Optional
import logging
from config import config


class AstrologerConfigLoader:
    """
    Loads and validates astrologer configuration from JSON file.
    Provides structured access to persona settings and behavior parameters.
    """
    
    def __init__(self, config_path: str = None):
        """
        Initialize configuration loader.
        
        Args:
            config_path (str): Path to astrologer.json file. Defaults to config value.
        """
        self.config_path = config_path or config.ASTROLOGER_CONFIG_PATH
        self.config_data: Optional[Dict[str, Any]] = None
        self.logger = logging.getLogger(__name__)
        
    def load_config(self) -> bool:
        """
        Load astrologer configuration from JSON file.
        
        Returns:
            bool: True if loading successful, False otherwise
        """
        try:
            if not os.path.exists(self.config_path):
                self.logger.error(f"Configuration file not found: {self.config_path}")
                return False
                
            with open(self.config_path, 'r', encoding='utf-8') as file:
                self.config_data = json.load(file)
                
            if self._validate_config():
                self.logger.info(f"Successfully loaded astrologer configuration from {self.config_path}")
                return True
            else:
                self.logger.error("Configuration validation failed")
                return False
                
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON in configuration file: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Error loading configuration: {e}")
            return False
            
    def _validate_config(self) -> bool:
        """
        Validate loaded configuration structure.
        
        Returns:
            bool: True if configuration is valid, False otherwise
        """
        if not self.config_data:
            return False
            
        required_sections = [
            'system_role',
            'communication_tone',
            'output_format'
        ]
        
        for section in required_sections:
            if section not in self.config_data:
                self.logger.error(f"Missing required configuration section: {section}")
                return False
                
        return True
        
    def get_system_prompt(self) -> str:
        """
        Generate system prompt from configuration.
        
        Returns:
            str: Formatted system prompt for the astrologer
        """
        if not self.config_data:
            return self._get_default_system_prompt()
            
        try:
            # Extract key configuration elements
            system_role = self.config_data.get('system_role', {})
            communication_tone = self.config_data.get('communication_tone', {})
            specialization = self.config_data.get('specialization_areas', {})
            
            # Build system prompt
            prompt_parts = []
            
            # Identity and expertise
            identity = system_role.get('identity', 'professional_astrologer')
            experience = system_role.get('years_experience', 20)
            prompt_parts.append(f"You are a {identity} with {experience} years of experience.")
            
            # Specialties
            specialties = system_role.get('specialties', [])
            if specialties:
                prompt_parts.append(f"Your specialties include: {', '.join(specialties)}.")
                
            # Communication style
            voice_chars = communication_tone.get('voice_characteristics', {})
            warmth = voice_chars.get('warmth_level', 'high')
            empathy = voice_chars.get('empathy', 'deep_understanding')
            prompt_parts.append(f"Communicate with {warmth} warmth and {empathy}.")
            
            # Professional approach
            primary_expertise = specialization.get('primary_expertise', [])
            if primary_expertise:
                prompt_parts.append(f"Focus on: {', '.join(primary_expertise)}.")
                
            # Ethical guidelines
            prompt_parts.append("Always maintain professional boundaries and encourage free will.")
            prompt_parts.append("Provide empowering guidance while being honest about challenges.")
            
            return " ".join(prompt_parts)
            
        except Exception as e:
            self.logger.error(f"Error generating system prompt: {e}")
            return self._get_default_system_prompt()
            
    def _get_default_system_prompt(self) -> str:
        """Get default system prompt if configuration loading fails."""
        return ("You are a professional astrologer with 20 years of experience. "
                "Provide empathetic, insightful guidance while maintaining professional boundaries. "
                "Focus on empowerment and personal growth through astrological wisdom.")
                
    def get_communication_style(self) -> Dict[str, Any]:
        """
        Get communication style settings.
        
        Returns:
            Dict: Communication style configuration
        """
        if not self.config_data:
            return {}
            
        return self.config_data.get('communication_tone', {})
        
    def get_output_format(self) -> Dict[str, Any]:
        """
        Get output format settings.
        
        Returns:
            Dict: Output format configuration
        """
        if not self.config_data:
            return {}
            
        return self.config_data.get('output_format', {})
        
    def get_specialization_areas(self) -> Dict[str, Any]:
        """
        Get specialization areas.
        
        Returns:
            Dict: Specialization configuration
        """
        if not self.config_data:
            return {}
            
        return self.config_data.get('specialization_areas', {})
        
    def get_full_config(self) -> Dict[str, Any]:
        """
        Get complete configuration data.
        
        Returns:
            Dict: Full configuration dictionary
        """
        return self.config_data or {}
        
    def is_loaded(self) -> bool:
        """
        Check if configuration is loaded.
        
        Returns:
            bool: True if configuration is loaded, False otherwise
        """
        return self.config_data is not None
        
    def reload_config(self) -> bool:
        """
        Reload configuration from file.
        
        Returns:
            bool: True if reload successful, False otherwise
        """
        self.config_data = None
        return self.load_config()
