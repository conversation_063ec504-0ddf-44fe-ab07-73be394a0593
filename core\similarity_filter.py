"""
Similarity threshold filtering component for RAG results.
Implements configurable threshold filtering with fallback behavior.
"""

from typing import List, Dict, Optional, Tuple
import logging
from config import config


class SimilarityFilter:
    """
    Filters RAG retrieval results based on similarity score thresholds.
    Provides fallback behavior when insufficient high-quality matches are found.
    """
    
    def __init__(self, 
                 threshold: float = None,
                 min_chunks: int = None,
                 enable_fallback: bool = None):
        """
        Initialize similarity filter with configurable parameters.
        
        Args:
            threshold (float): Minimum similarity score (0.0-1.0). Defaults to config value.
            min_chunks (int): Minimum number of chunks required. Defaults to config value.
            enable_fallback (bool): Enable fallback behavior. Defaults to config value.
        """
        self.threshold = threshold or config.SIMILARITY_THRESHOLD
        self.min_chunks = min_chunks or config.MIN_CONTEXT_CHUNKS
        self.enable_fallback = enable_fallback if enable_fallback is not None else config.ENABLE_RAG_FALLBACK
        
        # Validate threshold range
        if not 0.0 <= self.threshold <= 1.0:
            raise ValueError(f"Threshold must be between 0.0 and 1.0, got {self.threshold}")
            
        self.logger = logging.getLogger(__name__)
        
    def filter_results(self, matches: List[Dict]) -> Tuple[List[Dict], Dict]:
        """
        Filter RAG matches based on similarity threshold.
        
        Args:
            matches (List[Dict]): Raw matches from Pinecone query
            
        Returns:
            Tuple[List[Dict], Dict]: (filtered_matches, filter_stats)
        """
        if not matches:
            return [], self._create_stats(0, 0, "no_matches")
            
        # Extract scores and filter
        high_quality_matches = []
        for match in matches:
            score = match.get('score', 0.0)
            if score >= self.threshold:
                high_quality_matches.append(match)
                
        # Determine filtering outcome
        total_matches = len(matches)
        filtered_count = len(high_quality_matches)
        
        if filtered_count >= self.min_chunks:
            # Sufficient high-quality matches found
            result_matches = high_quality_matches
            status = "success"
            self.logger.info(f"Found {filtered_count} matches above threshold {self.threshold}")
            
        elif self.enable_fallback and matches:
            # Use fallback: return best available matches
            sorted_matches = sorted(matches, key=lambda x: x.get('score', 0.0), reverse=True)
            result_matches = sorted_matches[:self.min_chunks]
            status = "fallback"
            self.logger.warning(f"Using fallback: {filtered_count} matches above threshold, using top {len(result_matches)}")
            
        else:
            # No fallback or no matches at all
            result_matches = []
            status = "no_rag"
            self.logger.warning(f"No sufficient matches found. Threshold: {self.threshold}, Found: {filtered_count}")
            
        stats = self._create_stats(total_matches, filtered_count, status)
        return result_matches, stats
        
    def _create_stats(self, total: int, filtered: int, status: str) -> Dict:
        """Create filtering statistics dictionary."""
        return {
            "total_matches": total,
            "filtered_matches": filtered,
            "threshold_used": self.threshold,
            "min_chunks_required": self.min_chunks,
            "status": status,
            "fallback_enabled": self.enable_fallback
        }
        
    def validate_threshold(self, threshold: float) -> bool:
        """
        Validate if a threshold value is acceptable.
        
        Args:
            threshold (float): Threshold to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        return 0.0 <= threshold <= 1.0
        
    def update_threshold(self, new_threshold: float) -> bool:
        """
        Update the similarity threshold.
        
        Args:
            new_threshold (float): New threshold value
            
        Returns:
            bool: True if update successful, False otherwise
        """
        if self.validate_threshold(new_threshold):
            old_threshold = self.threshold
            self.threshold = new_threshold
            self.logger.info(f"Updated threshold from {old_threshold} to {new_threshold}")
            return True
        else:
            self.logger.error(f"Invalid threshold value: {new_threshold}")
            return False
            
    def get_filter_info(self) -> Dict:
        """
        Get current filter configuration information.
        
        Returns:
            Dict: Current filter settings
        """
        return {
            "threshold": self.threshold,
            "min_chunks": self.min_chunks,
            "enable_fallback": self.enable_fallback,
            "valid_threshold_range": "0.0 - 1.0"
        }
