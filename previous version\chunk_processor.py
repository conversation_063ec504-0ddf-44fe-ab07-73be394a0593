"""
PDF文本分割模組
目的：將PDF資料分割成適合embedding的文本塊
使用RecursiveCharacterTextSplitter進行智能分割
"""

import os
import hashlib
from typing import List, Dict, Optional
from datetime import datetime
import re

try:
    import PyPDF2
except ImportError:
    print("Warning: PyPDF2 not installed. Please install with: pip install PyPDF2")
    PyPDF2 = None

try:
    from langchain_text_splitters import RecursiveCharacterTextSplitter
except ImportError:
    print("Warning: langchain_text_splitters not installed. Please install with: pip install langchain-text-splitters")
    RecursiveCharacterTextSplitter = None


class PDFChunker:
    """
    PDF文本分割器 - 實現分層分塊策略

    分層策略：
    1. 段落級別提取：識別完整段落作為上下文單位
    2. 句子級別分割：將段落分割成句子進行embedding
    3. 增強metadata：每個句子包含完整段落上下文
    """

    def __init__(self, chunk_size: int = 250, chunk_overlap: int = 50, use_hierarchical: bool = True):
        """
        初始化PDF分割器

        Args:
            chunk_size (int): 每個chunk的目標字符數，預設250字
            chunk_overlap (int): chunk之間的重疊字符數，預設50字
            use_hierarchical (bool): 是否使用分層分塊策略，預設True
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.use_hierarchical = use_hierarchical

        if RecursiveCharacterTextSplitter is None:
            raise ImportError("langchain_text_splitters is required. Please install with: pip install langchain-text-splitters")

        # 初始化句子級別分割器
        self.sentence_splitter = RecursiveCharacterTextSplitter(
            chunk_size=200,  # 句子級別較小的chunk
            chunk_overlap=20,
            length_function=len,
            separators=[
                ". ",     # 英文句號+空格
                "! ",     # 英文驚嘆號+空格
                "? ",     # 英文問號+空格
                "; ",     # 英文分號+空格
                "\n",     # 行分隔
                " ",      # 空格
                ""        # 字符級分割
            ]
        )

        # 初始化傳統分割器（作為備用）
        self.traditional_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
            separators=[
                "\n\n",   # 段落分隔
                "\n",     # 行分隔
                ". ",     # 英文句號+空格
                "! ",     # 英文驚嘆號+空格
                "? ",     # 英文問號+空格
                "; ",     # 英文分號+空格
                ", ",     # 英文逗號+空格
                " ",      # 空格
                ""        # 字符級分割
            ]
        )
    
    def load_pdf(self, file_path: str) -> str:
        """
        讀取PDF文件內容
        
        Args:
            file_path (str): PDF文件路徑
            
        Returns:
            str: 提取的文本內容
        """
        if PyPDF2 is None:
            raise ImportError("PyPDF2 is required. Please install with: pip install PyPDF2")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"PDF文件不存在: {file_path}")
        
        text_content = ""
        
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                print(f"正在讀取PDF: {os.path.basename(file_path)}")
                print(f"總頁數: {len(pdf_reader.pages)}")
                
                for page_num, page in enumerate(pdf_reader.pages, 1):
                    try:
                        page_text = page.extract_text()
                        if page_text.strip():
                            text_content += f"\n\n--- 第{page_num}頁 ---\n\n"
                            text_content += page_text
                        
                        if page_num % 10 == 0:
                            print(f"已處理 {page_num} 頁...")
                            
                    except Exception as e:
                        print(f"警告: 第{page_num}頁讀取失敗: {str(e)}")
                        continue
                
                print(f"PDF讀取完成，總字符數: {len(text_content)}")
                
        except Exception as e:
            raise Exception(f"PDF讀取失敗: {str(e)}")
        
        return text_content

    def extract_paragraphs(self, text: str) -> List[Dict]:
        """
        提取段落並分析段落級別的上下文

        Args:
            text (str): 輸入文本

        Returns:
            List[Dict]: 段落列表，每個包含段落文本和metadata
        """
        # 按雙換行符分割段落
        raw_paragraphs = text.split('\n\n')

        paragraphs = []
        for i, para_text in enumerate(raw_paragraphs):
            # 清理段落文本
            clean_para = para_text.strip()

            # 跳過太短的段落或頁面標記
            if (len(clean_para) < 20 or
                re.match(r'^--- 第\d+頁 ---$', clean_para) or
                clean_para.isdigit()):
                continue

            # 提取段落主題/關鍵詞
            para_keywords = self.extract_keywords(clean_para, max_keywords=3)

            # 判斷段落類型
            para_type = self.classify_paragraph_type(clean_para)

            # 提取章節資訊
            chapter_info = self.extract_chapter_info(clean_para)

            paragraph_data = {
                "paragraph_id": i,
                "text": clean_para,
                "char_count": len(clean_para),
                "sentence_count": len(re.findall(r'[.!?]+', clean_para)),
                "keywords": para_keywords,
                "paragraph_type": para_type,
                "chapter": chapter_info,
                "themes": self.extract_paragraph_themes(clean_para)
            }

            paragraphs.append(paragraph_data)

        return paragraphs

    def classify_paragraph_type(self, paragraph: str) -> str:
        """
        分類段落類型

        Args:
            paragraph (str): 段落文本

        Returns:
            str: 段落類型
        """
        text_upper = paragraph[:100].upper()

        # 檢查是否為標題
        if (re.search(r'CHAPTER\s+\d+|PART\s+\d+|SECTION\s+\d+', text_upper) or
            re.search(r'^\d+\.\s*[A-Z]', paragraph[:50]) or
            (len(paragraph) < 100 and paragraph.isupper())):
            return "title"

        # 檢查是否為列表
        if re.search(r'^\s*[-•*]\s+', paragraph, re.MULTILINE):
            return "list"

        # 檢查是否為引用
        if paragraph.startswith('"') or paragraph.count('"') >= 2:
            return "quote"

        # 檢查是否為目錄或索引
        if "Table of Contents" in paragraph[:100] or "INDEX" in text_upper:
            return "index"

        # 檢查是否為腳註
        if re.search(r'^\d+\.\s+', paragraph) and len(paragraph) < 200:
            return "footnote"

        return "content"

    def extract_paragraph_themes(self, paragraph: str) -> List[str]:
        """
        提取段落主題

        Args:
            paragraph (str): 段落文本

        Returns:
            List[str]: 主題列表
        """
        themes = []

        # 占星學相關主題檢測
        astrology_patterns = {
            "planets": r'\b(sun|moon|mercury|venus|mars|jupiter|saturn|uranus|neptune|pluto)\b',
            "signs": r'\b(aries|taurus|gemini|cancer|leo|virgo|libra|scorpio|sagittarius|capricorn|aquarius|pisces)\b',
            "houses": r'\b(first|second|third|fourth|fifth|sixth|seventh|eighth|ninth|tenth|eleventh|twelfth)\s+house\b',
            "aspects": r'\b(conjunction|opposition|trine|square|sextile|quincunx)\b',
            "elements": r'\b(fire|earth|air|water)\s+(sign|element)\b',
            "modalities": r'\b(cardinal|fixed|mutable)\b'
        }

        text_lower = paragraph.lower()
        for theme, pattern in astrology_patterns.items():
            if re.search(pattern, text_lower, re.IGNORECASE):
                themes.append(theme)

        return themes

    def split_paragraph_to_sentences(self, paragraph_data: Dict) -> List[Dict]:
        """
        將段落分割成句子級別的chunks

        Args:
            paragraph_data (Dict): 段落數據

        Returns:
            List[Dict]: 句子級別的chunks
        """
        paragraph_text = paragraph_data["text"]

        # 使用句子分割器
        sentences = self.sentence_splitter.split_text(paragraph_text)

        sentence_chunks = []
        for i, sentence in enumerate(sentences):
            # 清理句子
            clean_sentence = sentence.strip()

            # 跳過太短的句子
            if len(clean_sentence) < 15:
                continue

            # 創建句子級別的chunk
            sentence_chunk = {
                "text": clean_sentence,
                "sentence_index": i,
                "sentence_position": f"{i+1}/{len(sentences)}",
                "paragraph_context": paragraph_text,  # 完整段落作為上下文
                "paragraph_id": paragraph_data["paragraph_id"],
                "paragraph_metadata": {
                    "keywords": paragraph_data["keywords"],
                    "themes": paragraph_data["themes"],
                    "paragraph_type": paragraph_data["paragraph_type"],
                    "chapter": paragraph_data["chapter"],
                    "char_count": paragraph_data["char_count"],
                    "sentence_count": paragraph_data["sentence_count"]
                }
            }

            sentence_chunks.append(sentence_chunk)

        return sentence_chunks

    def hierarchical_split_text_to_chunks(self, text: str, source_file: str) -> List[Dict]:
        """
        使用分層策略將文本分割成chunks

        Args:
            text (str): 輸入文本
            source_file (str): 來源文件路徑

        Returns:
            List[Dict]: 分層chunk列表
        """
        print(f"開始分層分割文本，總長度: {len(text)} 字符")

        # 第一步：提取段落
        paragraphs = self.extract_paragraphs(text)
        print(f"提取到 {len(paragraphs)} 個段落")

        # 第二步：將每個段落分割成句子
        all_chunks = []
        total_sentences = 0

        for para_data in paragraphs:
            sentence_chunks = self.split_paragraph_to_sentences(para_data)
            total_sentences += len(sentence_chunks)

            # 為每個句子chunk添加全局metadata
            for j, sentence_chunk in enumerate(sentence_chunks):
                # 提取頁面資訊
                page_match = re.search(r'--- 第(\d+)頁 ---', text)
                page_info = f"第{page_match.group(1)}頁" if page_match else ""

                # 生成唯一ID
                chunk_id = hashlib.md5(
                    f"{source_file}_{para_data['paragraph_id']}_{j}_{sentence_chunk['text'][:30]}"
                    .encode()
                ).hexdigest()[:12]

                # 創建增強的metadata
                enhanced_metadata = self.create_hierarchical_metadata(
                    sentence_chunk,
                    len(all_chunks),
                    source_file,
                    page_info
                )

                chunk_data = {
                    "text": sentence_chunk["text"],
                    "metadata": enhanced_metadata
                }

                all_chunks.append(chunk_data)

        print(f"分層分割完成：{len(paragraphs)} 段落 → {total_sentences} 句子chunks")
        return all_chunks

    def create_hierarchical_metadata(self, sentence_chunk: Dict, chunk_index: int,
                                   source_file: str, page_info: str = "") -> Dict:
        """
        為分層chunk創建增強的metadata

        Args:
            sentence_chunk (Dict): 句子chunk數據
            chunk_index (int): chunk索引
            source_file (str): 來源文件名
            page_info (str): 頁面資訊

        Returns:
            Dict: 增強的metadata字典
        """
        # 生成唯一ID
        chunk_id = hashlib.md5(
            f"{source_file}_{sentence_chunk['paragraph_id']}_{sentence_chunk['sentence_index']}_{sentence_chunk['text'][:30]}"
            .encode()
        ).hexdigest()[:12]

        # 提取句子級別的關鍵字
        sentence_keywords = self.extract_keywords(sentence_chunk["text"], max_keywords=3)

        # 判斷句子的內容類型
        sentence_type = self.classify_sentence_type(sentence_chunk["text"])

        # 創建分層metadata
        hierarchical_metadata = {
            # 基本資訊
            "chunk_id": chunk_id,
            "source": os.path.basename(source_file),
            "source_path": source_file,
            "chunk_index": chunk_index,
            "page_info": page_info,
            "created_at": datetime.now().isoformat(),

            # 句子級別資訊
            "sentence_text": sentence_chunk["text"],
            "sentence_index": sentence_chunk["sentence_index"],
            "sentence_position": sentence_chunk["sentence_position"],
            "sentence_char_count": len(sentence_chunk["text"]),
            "sentence_keywords": sentence_keywords,
            "sentence_type": sentence_type,

            # 段落級別上下文
            "paragraph_context": sentence_chunk["paragraph_context"],
            "paragraph_id": sentence_chunk["paragraph_id"],
            "paragraph_keywords": sentence_chunk["paragraph_metadata"]["keywords"],
            "paragraph_themes": sentence_chunk["paragraph_metadata"]["themes"],
            "paragraph_type": sentence_chunk["paragraph_metadata"]["paragraph_type"],
            "paragraph_char_count": sentence_chunk["paragraph_metadata"]["char_count"],
            "paragraph_sentence_count": sentence_chunk["paragraph_metadata"]["sentence_count"],

            # 文檔級別資訊
            "chapter": sentence_chunk["paragraph_metadata"]["chapter"],
            "content_type": "hierarchical_sentence",

            # 分層標識
            "chunking_strategy": "hierarchical",
            "granularity_level": "sentence",
            "context_level": "paragraph"
        }

        return hierarchical_metadata

    def classify_sentence_type(self, sentence: str) -> str:
        """
        分類句子類型

        Args:
            sentence (str): 句子文本

        Returns:
            str: 句子類型
        """
        # 檢查是否為問句
        if sentence.strip().endswith('?'):
            return "question"

        # 檢查是否為感嘆句
        if sentence.strip().endswith('!'):
            return "exclamation"

        # 檢查是否為定義句
        if " is " in sentence.lower() or " are " in sentence.lower():
            return "definition"

        # 檢查是否為列表項
        if re.match(r'^\s*[-•*]\s+', sentence):
            return "list_item"

        # 檢查是否為引用
        if sentence.startswith('"') or sentence.endswith('"'):
            return "quote"

        return "statement"
    
    def extract_keywords(self, text: str, max_keywords: int = 5) -> List[str]:
        """
        從文本中提取關鍵字
        
        Args:
            text (str): 輸入文本
            max_keywords (int): 最大關鍵字數量
            
        Returns:
            List[str]: 關鍵字列表
        """
        # 英文關鍵字提取邏輯
        # 移除標點符號，保留字母和數字
        clean_text = re.sub(r'[^\w\s]', ' ', text)

        # 分詞（按空格分割）
        words = clean_text.lower().split()

        # 英文停用詞
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
            'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
            'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must', 'shall',
            'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they',
            'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their',
            'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below',
            'not', 'no', 'nor', 'so', 'than', 'too', 'very', 'just', 'now', 'here', 'there', 'when', 'where',
            'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such'
        }
        keywords = [word for word in words if len(word) > 2 and word not in stop_words and word.isalpha()]
        
        # 統計詞頻並取前N個
        word_freq = {}
        for word in keywords:
            word_freq[word] = word_freq.get(word, 0) + 1
        
        # 按頻率排序並取前max_keywords個
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return [word for word, freq in sorted_words[:max_keywords]]
    
    def extract_chapter_info(self, text: str) -> str:
        """
        嘗試從文本中提取章節資訊
        
        Args:
            text (str): 輸入文本
            
        Returns:
            str: 章節名稱或空字符串
        """
        # 尋找英文章節標題的模式
        chapter_patterns = [
            r'Chapter\s+(\d+)[:\s]*([^\n]+)',           # Chapter 1: Title
            r'CHAPTER\s+(\d+)[:\s]*([^\n]+)',           # CHAPTER 1: Title
            r'Part\s+(\d+)[:\s]*([^\n]+)',              # Part 1: Title
            r'PART\s+(\d+)[:\s]*([^\n]+)',              # PART 1: Title
            r'Section\s+(\d+)[:\s]*([^\n]+)',           # Section 1: Title
            r'(\d+)\.\s*([A-Z][^\n]+)',                 # 1. Title
            r'^([A-Z][A-Z\s]+)$',                       # ALL CAPS TITLE
            r'^([A-Z][a-z\s]+[A-Z][a-z\s]*)$',         # Title Case
        ]
        
        for pattern in chapter_patterns:
            match = re.search(pattern, text[:200], re.MULTILINE)  # 只搜索前200字符
            if match:
                # 根據不同的模式返回適當的組
                if len(match.groups()) >= 2:
                    # 有編號和標題的模式 (如 Chapter 1: Title)
                    return f"{match.group(1)}: {match.group(2).strip()}"
                else:
                    # 只有標題的模式
                    return match.group(1).strip()

        return ""
    
    def create_metadata(self, chunk_text: str, chunk_index: int, source_file: str, 
                       page_info: str = "") -> Dict:
        """
        為chunk創建metadata
        
        Args:
            chunk_text (str): chunk文本內容
            chunk_index (int): chunk索引
            source_file (str): 來源文件名
            page_info (str): 頁面資訊
            
        Returns:
            Dict: metadata字典
        """
        # 生成唯一ID
        chunk_id = hashlib.md5(f"{source_file}_{chunk_index}_{chunk_text[:50]}".encode()).hexdigest()[:12]
        
        # 提取關鍵字
        keywords = self.extract_keywords(chunk_text)
        
        # 提取章節資訊
        chapter = self.extract_chapter_info(chunk_text)
        
        # 判斷內容類型（英文）
        content_type = "content"
        text_start = chunk_text[:100].upper()

        # 檢查是否為標題
        if (re.search(r'CHAPTER\s+\d+|PART\s+\d+|SECTION\s+\d+', text_start) or
            re.search(r'^\d+\.\s*[A-Z]', chunk_text[:50]) or
            (len(chunk_text) < 100 and chunk_text.isupper())):
            content_type = "title"
        elif len(chunk_text) < 50:
            content_type = "fragment"
        elif "Table of Contents" in chunk_text[:100] or "INDEX" in text_start:
            content_type = "index"
        
        metadata = {
            "chunk_id": chunk_id,
            "source": os.path.basename(source_file),
            "source_path": source_file,
            "chunk_index": chunk_index,
            "page_info": page_info,
            "chapter": chapter,
            "content_type": content_type,
            "char_count": len(chunk_text),
            "keywords": keywords,
            "created_at": datetime.now().isoformat(),
        }
        
        return metadata
    
    def split_text_to_chunks(self, text: str, source_file: str) -> List[Dict]:
        """
        將文本分割成chunks - 支持分層和傳統策略

        Args:
            text (str): 輸入文本
            source_file (str): 來源文件路徑

        Returns:
            List[Dict]: chunk列表，每個包含text和metadata
        """
        if self.use_hierarchical:
            print(f"使用分層分塊策略，總長度: {len(text)} 字符")
            return self.hierarchical_split_text_to_chunks(text, source_file)
        else:
            print(f"使用傳統分塊策略，總長度: {len(text)} 字符")
            return self.traditional_split_text_to_chunks(text, source_file)

    def traditional_split_text_to_chunks(self, text: str, source_file: str) -> List[Dict]:
        """
        傳統的文本分割方法

        Args:
            text (str): 輸入文本
            source_file (str): 來源文件路徑

        Returns:
            List[Dict]: chunk列表，每個包含text和metadata
        """
        # 使用傳統分割器
        text_chunks = self.traditional_splitter.split_text(text)

        print(f"傳統分割完成，共生成 {len(text_chunks)} 個chunks")

        chunks_with_metadata = []

        for i, chunk_text in enumerate(text_chunks):
            # 提取頁面資訊
            page_match = re.search(r'--- 第(\d+)頁 ---', chunk_text)
            page_info = f"第{page_match.group(1)}頁" if page_match else ""

            # 清理chunk文本（移除頁面標記）
            clean_chunk = re.sub(r'--- 第\d+頁 ---\n*', '', chunk_text).strip()

            if len(clean_chunk) < 10:  # 跳過太短的chunks
                continue

            # 創建metadata
            metadata = self.create_metadata(clean_chunk, i, source_file, page_info)

            chunk_data = {
                "text": clean_chunk,
                "metadata": metadata
            }

            chunks_with_metadata.append(chunk_data)

        print(f"有效chunks數量: {len(chunks_with_metadata)}")
        return chunks_with_metadata
    
    def process_pdf_to_chunks(self, pdf_path: str) -> List[Dict]:
        """
        處理PDF文件，返回chunks列表
        
        Args:
            pdf_path (str): PDF文件路徑
            
        Returns:
            List[Dict]: chunks列表
        """
        print(f"\n=== 開始處理PDF文件: {os.path.basename(pdf_path)} ===")
        
        # 讀取PDF
        text_content = self.load_pdf(pdf_path)
        
        if not text_content.strip():
            print("警告: PDF文件內容為空")
            return []
        
        # 分割成chunks
        chunks = self.split_text_to_chunks(text_content, pdf_path)
        
        print(f"=== PDF處理完成，共生成 {len(chunks)} 個chunks ===\n")
        
        return chunks


def process_all_pdfs(data_dir: str = "./data", chunk_size: int = 250, chunk_overlap: int = 50,
                    use_hierarchical: bool = True) -> List[Dict]:
    """
    處理指定目錄中的所有PDF文件

    Args:
        data_dir (str): PDF文件目錄
        chunk_size (int): chunk大小
        chunk_overlap (int): chunk重疊大小
        use_hierarchical (bool): 是否使用分層分塊策略

    Returns:
        List[Dict]: 所有chunks的列表
    """
    if not os.path.exists(data_dir):
        raise FileNotFoundError(f"資料目錄不存在: {data_dir}")

    # 初始化chunker
    chunker = PDFChunker(
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        use_hierarchical=use_hierarchical
    )

    # 尋找所有PDF文件
    pdf_files = [f for f in os.listdir(data_dir) if f.lower().endswith('.pdf')]

    if not pdf_files:
        print(f"在 {data_dir} 目錄中未找到PDF文件")
        return []

    strategy_name = "分層分塊" if use_hierarchical else "傳統分塊"
    print(f"使用 {strategy_name} 策略處理 {len(pdf_files)} 個PDF文件:")
    for pdf_file in pdf_files:
        print(f"  - {pdf_file}")

    all_chunks = []

    for pdf_file in pdf_files:
        pdf_path = os.path.join(data_dir, pdf_file)
        try:
            chunks = chunker.process_pdf_to_chunks(pdf_path)
            all_chunks.extend(chunks)
            print(f"✅ {pdf_file}: 成功處理 {len(chunks)} 個chunks")
        except Exception as e:
            print(f"❌ {pdf_file}: 處理失敗 - {str(e)}")

    print(f"\n📊 總計處理結果 ({strategy_name}):")
    print(f"   - 處理文件數: {len(pdf_files)}")
    print(f"   - 總chunks數: {len(all_chunks)}")
    print(f"   - 平均每文件: {len(all_chunks)/len(pdf_files):.1f} chunks")

    # 如果使用分層策略，顯示額外統計
    if use_hierarchical and all_chunks:
        hierarchical_chunks = [c for c in all_chunks if c['metadata'].get('chunking_strategy') == 'hierarchical']
        if hierarchical_chunks:
            print(f"   - 分層chunks: {len(hierarchical_chunks)}")
            print(f"   - 句子級別chunks: {len([c for c in hierarchical_chunks if c['metadata'].get('granularity_level') == 'sentence'])}")

            # 統計段落數量
            unique_paragraphs = set()
            for chunk in hierarchical_chunks:
                para_id = chunk['metadata'].get('paragraph_id')
                source = chunk['metadata'].get('source')
                if para_id is not None and source:
                    unique_paragraphs.add(f"{source}_{para_id}")
            print(f"   - 唯一段落數: {len(unique_paragraphs)}")
    
    return all_chunks


def compare_chunking_strategies(data_dir: str = "./data") -> None:
    """
    比較分層和傳統分塊策略的效果

    Args:
        data_dir (str): PDF文件目錄
    """
    print("🔍 比較分塊策略效果")
    print("=" * 60)

    # 測試傳統策略
    print("\n📊 傳統分塊策略:")
    traditional_chunks = process_all_pdfs(data_dir, use_hierarchical=False)

    print("\n📊 分層分塊策略:")
    hierarchical_chunks = process_all_pdfs(data_dir, use_hierarchical=True)

    # 比較結果
    print(f"\n🔍 策略比較:")
    print(f"   - 傳統策略chunks數: {len(traditional_chunks)}")
    print(f"   - 分層策略chunks數: {len(hierarchical_chunks)}")
    print(f"   - 數量比例: {len(hierarchical_chunks)/len(traditional_chunks):.2f}x")

    # 顯示範例
    if traditional_chunks and hierarchical_chunks:
        print(f"\n📋 傳統策略範例:")
        trad_example = traditional_chunks[0]
        print(f"   文本長度: {len(trad_example['text'])} 字符")
        print(f"   關鍵字: {trad_example['metadata'].get('keywords', [])}")
        print(f"   內容類型: {trad_example['metadata'].get('content_type', 'unknown')}")
        print(f"   文本預覽: {trad_example['text'][:150]}...")

        print(f"\n📋 分層策略範例:")
        hier_example = hierarchical_chunks[0]
        print(f"   句子文本長度: {len(hier_example['text'])} 字符")
        print(f"   句子關鍵字: {hier_example['metadata'].get('sentence_keywords', [])}")
        print(f"   段落關鍵字: {hier_example['metadata'].get('paragraph_keywords', [])}")
        print(f"   段落主題: {hier_example['metadata'].get('paragraph_themes', [])}")
        print(f"   句子位置: {hier_example['metadata'].get('sentence_position', 'unknown')}")
        print(f"   分塊策略: {hier_example['metadata'].get('chunking_strategy', 'unknown')}")
        print(f"   句子文本: {hier_example['text']}")
        print(f"   段落上下文預覽: {hier_example['metadata'].get('paragraph_context', '')[:200]}...")


if __name__ == "__main__":
    # 測試代碼
    try:
        print("🧪 測試分層分塊策略")
        print("=" * 50)

        # 選擇測試模式
        import sys
        if len(sys.argv) > 1 and sys.argv[1] == "compare":
            # 比較兩種策略
            compare_chunking_strategies()
        else:
            # 只測試分層策略
            chunks = process_all_pdfs(use_hierarchical=True)

            if chunks:
                print(f"\n📋 分層策略範例chunk:")
                example_chunk = chunks[0]
                metadata = example_chunk['metadata']

                print(f"句子文本: {example_chunk['text']}")
                print(f"句子長度: {len(example_chunk['text'])} 字符")
                print(f"來源: {metadata.get('source', 'unknown')}")
                print(f"句子關鍵字: {metadata.get('sentence_keywords', [])}")
                print(f"段落關鍵字: {metadata.get('paragraph_keywords', [])}")
                print(f"段落主題: {metadata.get('paragraph_themes', [])}")
                print(f"句子位置: {metadata.get('sentence_position', 'unknown')}")
                print(f"段落類型: {metadata.get('paragraph_type', 'unknown')}")
                print(f"分塊策略: {metadata.get('chunking_strategy', 'unknown')}")
                print(f"粒度級別: {metadata.get('granularity_level', 'unknown')}")
                print(f"上下文級別: {metadata.get('context_level', 'unknown')}")

                if metadata.get('paragraph_context'):
                    print(f"\n📄 段落上下文:")
                    print(f"{metadata['paragraph_context'][:300]}...")

    except Exception as e:
        print(f"錯誤: {str(e)}")
        import traceback
        traceback.print_exc()
