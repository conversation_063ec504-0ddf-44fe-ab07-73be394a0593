import{b$ as de,cw as ce,cx as pe,cy as fe,cz as ge,cA as ye,ce as me,bv as ve,bS as be,cB as he,cf as Se,cC as Oe,d as K,cD as Ce,s as Ie,r as V,b8 as Pe,N as J,z as $e,C as _e,j as z,br as Te,bH as Ee,bs as Re,b9 as we,bt as De,b_ as xe}from"./index.CbQtRkVt.js";import{a as Ae}from"./useBasicWidgetState.Bx3VaRHk.js";import{T as We}from"./timepicker.DOMbfm_a.js";import"./FormClearHelper.Cdw5Y7_m.js";import"./possibleConstructorReturn.C5GiK_Ob.js";import"./createSuper.C5k_2vfB.js";var Ne=de.Consumer;const Be=Object.freeze(Object.defineProperty({__proto__:null,ThemeConsumer:Ne,ThemeProvider:ce,createThemedStyled:pe,createThemedUseStyletron:fe,createThemedWithStyle:ge,expandBorderStyles:ye,hexToRgb:me,styled:ve,useStyletron:be,withStyle:he,withWrapper:Se},Symbol.toStringTag,{value:"Module"}));var C={};const Y=Oe(Be);var w={},X;function Me(){if(X)return w;X=1,Object.defineProperty(w,"__esModule",{value:!0}),w.STATE_CHANGE_TYPE=w.SIZE=w.ENHANCER_POSITION=w.CUSTOM_INPUT_TYPE=w.ADJOINED=void 0;var g={change:"change"};w.STATE_CHANGE_TYPE=g;var c={textarea:"textarea"};w.CUSTOM_INPUT_TYPE=c;var h={none:"none",left:"left",right:"right",both:"both"};w.ADJOINED=h;var m={mini:"mini",default:"default",compact:"compact",large:"large"};w.SIZE=m;var b={start:"start",end:"end"};return w.ENHANCER_POSITION=b,w}var q={},A={},H={},Q;function Le(){if(Q)return H;Q=1,Object.defineProperty(H,"__esModule",{value:!0}),H.default=c;function g(m){"@babel/helpers - typeof";return g=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(b){return typeof b}:function(b){return b&&typeof Symbol=="function"&&b.constructor===Symbol&&b!==Symbol.prototype?"symbol":typeof b},g(m)}function c(m){m=m||{};for(var b=arguments.length<=1?0:arguments.length-1,v,y,S=0;S<b;S++){v=(S+1<1||arguments.length<=S+1?void 0:arguments[S+1])||{};for(var f in v)g(v[f])!==void 0&&(y=v[f],h(y)?m[f]=c(m[f]||Array.isArray(y)&&[]||{},y):m[f]=y)}return m}function h(m){return Array.isArray(m)||{}.toString.call(m)=="[object Object]"}return H}var ee;function ae(){if(ee)return A;ee=1,Object.defineProperty(A,"__esModule",{value:!0}),A.getOverride=E,A.getOverrideProps=_,A.getOverrides=L,A.mergeConfigurationOverrides=M,A.mergeOverride=x,A.mergeOverrides=B,A.toObjectOverride=D,A.useOverrides=F;var g=v(K()),c=Ce(),h=m(Le());function m(i){return i&&i.__esModule?i:{default:i}}function b(i){if(typeof WeakMap!="function")return null;var t=new WeakMap,u=new WeakMap;return(b=function(r){return r?u:t})(i)}function v(i,t){if(i&&i.__esModule)return i;if(i===null||P(i)!=="object"&&typeof i!="function")return{default:i};var u=b(t);if(u&&u.has(i))return u.get(i);var s={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in i)if(a!=="default"&&Object.prototype.hasOwnProperty.call(i,a)){var l=r?Object.getOwnPropertyDescriptor(i,a):null;l&&(l.get||l.set)?Object.defineProperty(s,a,l):s[a]=i[a]}return s.default=i,u&&u.set(i,s),s}function y(){return y=Object.assign?Object.assign.bind():function(i){for(var t=1;t<arguments.length;t++){var u=arguments[t];for(var s in u)Object.prototype.hasOwnProperty.call(u,s)&&(i[s]=u[s])}return i},y.apply(this,arguments)}function S(i,t){var u=Object.keys(i);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(i);t&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(i,r).enumerable})),u.push.apply(u,s)}return u}function f(i){for(var t=1;t<arguments.length;t++){var u=arguments[t]!=null?arguments[t]:{};t%2?S(Object(u),!0).forEach(function(s){$(i,s,u[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(u)):S(Object(u)).forEach(function(s){Object.defineProperty(i,s,Object.getOwnPropertyDescriptor(u,s))})}return i}function $(i,t,u){return t in i?Object.defineProperty(i,t,{value:u,enumerable:!0,configurable:!0,writable:!0}):i[t]=u,i}function P(i){"@babel/helpers - typeof";return P=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},P(i)}function E(i){return(0,c.isValidElementType)(i)?i:i&&P(i)==="object"?i.component:i}function _(i){return i&&P(i)==="object"?P(i.props)==="object"?f(f({},i.props),{},{$style:i.style}):{$style:i.style}:{}}function D(i){return(0,c.isValidElementType)(i)?{component:i}:i||{}}function L(i,t){var u=E(i)||t;if(i&&P(i)==="object"&&typeof i.props=="function"){var s=g.forwardRef(function(a,l){var n=i.props(a),e=_(f(f({},i),{},{props:n}));return g.createElement(u,y({ref:l},e))});return s.displayName=u.displayName,[s,{}]}var r=_(i);return[u,r]}function B(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},u=Object.assign({},i,t),s=Object.keys(u);return s.reduce(function(r,a){return r[a]=x(D(i[a]),D(t[a])),r},{})}function x(i,t){var u=f(f({},i),t);return i.props&&t.props&&(u.props=M(i.props,t.props)),i.style&&t.style&&(u.style=M(i.style,t.style)),u}function M(i,t){return P(i)==="object"&&P(t)==="object"?(0,h.default)({},i,t):function(){return(0,h.default)({},typeof i=="function"?i.apply(void 0,arguments):i,typeof t=="function"?t.apply(void 0,arguments):t)}}function F(i){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return g.useMemo(function(){return Object.keys(i).reduce(function(u,s){return u[s]=L(t[s],i[s]),u},{})},[t])}return A}var j={},Z={},te;function Fe(){if(te)return Z;te=1,Object.defineProperty(Z,"__esModule",{value:!0}),Z.Svg=void 0,Z.getSvgStyles=c;var g=Y;function c(m){var b=m.$theme,v=m.$size,y=m.$color,S=b.sizing.scale600;v&&(b.sizing[v]?S=b.sizing[v]:typeof v=="number"?S="".concat(v,"px"):S=v);var f="currentColor";return y&&(b.colors[y]?f=b.colors[y]:f=y),{display:"inline-block",fill:f,color:f,height:S,width:S}}var h=(0,g.styled)("svg",c);return Z.Svg=h,h.displayName="Svg",h.displayName="Svg",Z}var k={},re;function ze(){if(re)return k;re=1,Object.defineProperty(k,"__esModule",{value:!0}),k.default=g;function g(c){var h={};for(var m in c)m[0]!=="$"&&(h[m]=c[m]);return h}return k}var ne;function Ze(){if(ne)return j;ne=1;function g(r){"@babel/helpers - typeof";return g=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(a){return typeof a}:function(a){return a&&typeof Symbol=="function"&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},g(r)}Object.defineProperty(j,"__esModule",{value:!0}),j.default=void 0;var c=f(K()),h=ae(),m=Fe(),b=y(ze()),v=["children","title","size","color","overrides"];function y(r){return r&&r.__esModule?r:{default:r}}function S(r){if(typeof WeakMap!="function")return null;var a=new WeakMap,l=new WeakMap;return(S=function(e){return e?l:a})(r)}function f(r,a){if(r&&r.__esModule)return r;if(r===null||g(r)!=="object"&&typeof r!="function")return{default:r};var l=S(a);if(l&&l.has(r))return l.get(r);var n={},e=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in r)if(o!=="default"&&Object.prototype.hasOwnProperty.call(r,o)){var p=e?Object.getOwnPropertyDescriptor(r,o):null;p&&(p.get||p.set)?Object.defineProperty(n,o,p):n[o]=r[o]}return n.default=r,l&&l.set(r,n),n}function $(){return $=Object.assign?Object.assign.bind():function(r){for(var a=1;a<arguments.length;a++){var l=arguments[a];for(var n in l)Object.prototype.hasOwnProperty.call(l,n)&&(r[n]=l[n])}return r},$.apply(this,arguments)}function P(r,a){var l=Object.keys(r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(r);a&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})),l.push.apply(l,n)}return l}function E(r){for(var a=1;a<arguments.length;a++){var l=arguments[a]!=null?arguments[a]:{};a%2?P(Object(l),!0).forEach(function(n){_(r,n,l[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(l)):P(Object(l)).forEach(function(n){Object.defineProperty(r,n,Object.getOwnPropertyDescriptor(l,n))})}return r}function _(r,a,l){return a in r?Object.defineProperty(r,a,{value:l,enumerable:!0,configurable:!0,writable:!0}):r[a]=l,r}function D(r,a){return F(r)||M(r,a)||B(r,a)||L()}function L(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function B(r,a){if(r){if(typeof r=="string")return x(r,a);var l=Object.prototype.toString.call(r).slice(8,-1);if(l==="Object"&&r.constructor&&(l=r.constructor.name),l==="Map"||l==="Set")return Array.from(r);if(l==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(l))return x(r,a)}}function x(r,a){(a==null||a>r.length)&&(a=r.length);for(var l=0,n=new Array(a);l<a;l++)n[l]=r[l];return n}function M(r,a){var l=r==null?null:typeof Symbol<"u"&&r[Symbol.iterator]||r["@@iterator"];if(l!=null){var n=[],e=!0,o=!1,p,d;try{for(l=l.call(r);!(e=(p=l.next()).done)&&(n.push(p.value),!(a&&n.length===a));e=!0);}catch(O){o=!0,d=O}finally{try{!e&&l.return!=null&&l.return()}finally{if(o)throw d}}return n}}function F(r){if(Array.isArray(r))return r}function i(r,a){if(r==null)return{};var l=t(r,a),n,e;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(r);for(e=0;e<o.length;e++)n=o[e],!(a.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(r,n)&&(l[n]=r[n])}return l}function t(r,a){if(r==null)return{};var l={},n=Object.keys(r),e,o;for(o=0;o<n.length;o++)e=n[o],!(a.indexOf(e)>=0)&&(l[e]=r[e]);return l}var u=function(a,l){var n=a.children,e=a.title,o=a.size,p=a.color,d=a.overrides,O=d===void 0?{}:d,I=i(a,v),R=(0,h.getOverrides)(O.Svg,m.Svg),W=D(R,2),T=W[0],N=W[1],U=T.__STYLETRON__?E(E({title:e,$color:p,$size:o},I),N):E(E({title:e,color:p,size:o},(0,b.default)(I)),(0,b.default)(N));return c.createElement(T,$({"data-baseweb":"icon",ref:l},U),e?c.createElement("title",null,e):null,n)},s=c.forwardRef(u);return j.default=s,j}var oe;function qe(){if(oe)return q;oe=1;function g(t){"@babel/helpers - typeof";return g=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(u){return typeof u}:function(u){return u&&typeof Symbol=="function"&&u.constructor===Symbol&&u!==Symbol.prototype?"symbol":typeof u},g(t)}Object.defineProperty(q,"__esModule",{value:!0}),q.default=void 0;var c=f(K()),h=Y,m=ae(),b=y(Ze()),v=["title","size","color","overrides"];function y(t){return t&&t.__esModule?t:{default:t}}function S(t){if(typeof WeakMap!="function")return null;var u=new WeakMap,s=new WeakMap;return(S=function(a){return a?s:u})(t)}function f(t,u){if(t&&t.__esModule)return t;if(t===null||g(t)!=="object"&&typeof t!="function")return{default:t};var s=S(u);if(s&&s.has(t))return s.get(t);var r={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in t)if(l!=="default"&&Object.prototype.hasOwnProperty.call(t,l)){var n=a?Object.getOwnPropertyDescriptor(t,l):null;n&&(n.get||n.set)?Object.defineProperty(r,l,n):r[l]=t[l]}return r.default=t,s&&s.set(t,r),r}function $(){return $=Object.assign?Object.assign.bind():function(t){for(var u=1;u<arguments.length;u++){var s=arguments[u];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(t[r]=s[r])}return t},$.apply(this,arguments)}function P(t,u){if(t==null)return{};var s=E(t,u),r,a;if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(t);for(a=0;a<l.length;a++)r=l[a],!(u.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(s[r]=t[r])}return s}function E(t,u){if(t==null)return{};var s={},r=Object.keys(t),a,l;for(l=0;l<r.length;l++)a=r[l],!(u.indexOf(a)>=0)&&(s[a]=t[a]);return s}function _(t,u){return M(t)||x(t,u)||L(t,u)||D()}function D(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function L(t,u){if(t){if(typeof t=="string")return B(t,u);var s=Object.prototype.toString.call(t).slice(8,-1);if(s==="Object"&&t.constructor&&(s=t.constructor.name),s==="Map"||s==="Set")return Array.from(t);if(s==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s))return B(t,u)}}function B(t,u){(u==null||u>t.length)&&(u=t.length);for(var s=0,r=new Array(u);s<u;s++)r[s]=t[s];return r}function x(t,u){var s=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(s!=null){var r=[],a=!0,l=!1,n,e;try{for(s=s.call(t);!(a=(n=s.next()).done)&&(r.push(n.value),!(u&&r.length===u));a=!0);}catch(o){l=!0,e=o}finally{try{!a&&s.return!=null&&s.return()}finally{if(l)throw e}}return r}}function M(t){if(Array.isArray(t))return t}function F(t,u){var s=(0,h.useStyletron)(),r=_(s,2),a=r[1],l=t.title,n=l===void 0?"Delete Alt":l,e=t.size,o=t.color,p=t.overrides,d=p===void 0?{}:p,O=P(t,v),I=(0,m.mergeOverride)({component:a.icons&&a.icons.DeleteAlt?a.icons.DeleteAlt:null},d&&d.Svg?(0,m.toObjectOverride)(d.Svg):{});return c.createElement(b.default,$({viewBox:"0 0 24 24",ref:u,title:n,size:e,color:o,overrides:{Svg:I}},O),c.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 20C16.4183 20 20 16.4183 20 12C20 7.58173 16.4183 4 12 4C7.58173 4 4 7.58173 4 12C4 16.4183 7.58173 20 12 20ZM10.0303 8.96967C9.73743 8.67679 9.26257 8.67679 8.96967 8.96967C8.67676 9.26257 8.67676 9.73743 8.96967 10.0303L10.9393 12L8.96967 13.9697C8.67676 14.2626 8.67676 14.7374 8.96967 15.0303C9.26257 15.3232 9.73743 15.3232 10.0303 15.0303L12 13.0607L13.9697 15.0303C14.2626 15.3232 14.7374 15.3232 15.0303 15.0303C15.3232 14.7374 15.3232 14.2626 15.0303 13.9697L13.0607 12L15.0303 10.0303C15.3232 9.73743 15.3232 9.26257 15.0303 8.96967C14.7374 8.67679 14.2626 8.67679 13.9697 8.96967L12 10.9393L10.0303 8.96967Z"}))}var i=c.forwardRef(F);return q.default=i,q}var ie;function je(){if(ie)return C;ie=1,Object.defineProperty(C,"__esModule",{value:!0}),C.getRootStyles=C.getInputStyles=C.getInputContainerStyles=C.StyledMaskToggleButton=C.StyledClearIconContainer=C.StyledClearIcon=C.Root=C.InputEnhancer=C.InputContainer=C.Input=void 0;var g=Y,c=Me(),h=m(qe());function m(n){return n&&n.__esModule?n:{default:n}}function b(n,e){var o=Object.keys(n);if(Object.getOwnPropertySymbols){var p=Object.getOwnPropertySymbols(n);e&&(p=p.filter(function(d){return Object.getOwnPropertyDescriptor(n,d).enumerable})),o.push.apply(o,p)}return o}function v(n){for(var e=1;e<arguments.length;e++){var o=arguments[e]!=null?arguments[e]:{};e%2?b(Object(o),!0).forEach(function(p){y(n,p,o[p])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(o)):b(Object(o)).forEach(function(p){Object.defineProperty(n,p,Object.getOwnPropertyDescriptor(o,p))})}return n}function y(n,e,o){return e in n?Object.defineProperty(n,e,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[e]=o,n}var S=(0,g.styled)("button",function(n){var e,o=n.$theme,p=n.$size,d=n.$isFocusVisible,O=(e={},y(e,c.SIZE.mini,o.sizing.scale400),y(e,c.SIZE.compact,o.sizing.scale400),y(e,c.SIZE.default,o.sizing.scale300),y(e,c.SIZE.large,o.sizing.scale200),e)[p];return{display:"flex",alignItems:"center",borderTopStyle:"none",borderBottomStyle:"none",borderLeftStyle:"none",borderRightStyle:"none",background:"none",paddingLeft:O,paddingRight:O,outline:d?"solid 3px ".concat(o.colors.accent):"none",color:o.colors.contentPrimary}});C.StyledMaskToggleButton=S,S.displayName="StyledMaskToggleButton",S.displayName="StyledMaskToggleButton";var f=(0,g.styled)("div",function(n){var e,o=n.$alignTop,p=o===void 0?!1:o,d=n.$size,O=n.$theme,I=(e={},y(e,c.SIZE.mini,O.sizing.scale200),y(e,c.SIZE.compact,O.sizing.scale200),y(e,c.SIZE.default,O.sizing.scale100),y(e,c.SIZE.large,O.sizing.scale0),e)[d];return{display:"flex",alignItems:p?"flex-start":"center",paddingLeft:I,paddingRight:I,paddingTop:p?O.sizing.scale500:"0px",color:O.colors.contentPrimary}});C.StyledClearIconContainer=f,f.displayName="StyledClearIconContainer",f.displayName="StyledClearIconContainer";var $=(0,g.styled)(h.default,function(n){var e=n.$theme,o=n.$isFocusVisible;return{cursor:"pointer",outline:o?"solid 3px ".concat(e.colors.accent):"none"}});C.StyledClearIcon=$,$.displayName="StyledClearIcon",$.displayName="StyledClearIcon";function P(n,e){var o;return(o={},y(o,c.SIZE.mini,{paddingTop:e.scale100,paddingBottom:e.scale100,paddingLeft:e.scale550,paddingRight:e.scale550}),y(o,c.SIZE.compact,{paddingTop:e.scale200,paddingBottom:e.scale200,paddingLeft:e.scale550,paddingRight:e.scale550}),y(o,c.SIZE.default,{paddingTop:e.scale400,paddingBottom:e.scale400,paddingLeft:e.scale550,paddingRight:e.scale550}),y(o,c.SIZE.large,{paddingTop:e.scale550,paddingBottom:e.scale550,paddingLeft:e.scale550,paddingRight:e.scale550}),o)[n]}function E(n,e,o,p,d){var O=n===c.ADJOINED.both||n===c.ADJOINED.left&&p!=="rtl"||n===c.ADJOINED.right&&p==="rtl"||d&&p==="rtl",I=n===c.ADJOINED.both||n===c.ADJOINED.right&&p!=="rtl"||n===c.ADJOINED.left&&p==="rtl"||d&&p!=="rtl";return{paddingLeft:O?o.scale550:"0px",paddingRight:I?o.scale550:"0px"}}function _(n,e){var o;return(o={},y(o,c.SIZE.mini,e.font100),y(o,c.SIZE.compact,e.font200),y(o,c.SIZE.default,e.font300),y(o,c.SIZE.large,e.font400),o)[n]}function D(n,e,o){var p=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1,d=arguments.length>4?arguments[4]:void 0;return n?{borderLeftColor:d.inputFillDisabled,borderRightColor:d.inputFillDisabled,borderTopColor:d.inputFillDisabled,borderBottomColor:d.inputFillDisabled,backgroundColor:d.inputFillDisabled}:e?{borderLeftColor:d.borderSelected,borderRightColor:d.borderSelected,borderTopColor:d.borderSelected,borderBottomColor:d.borderSelected,backgroundColor:d.inputFillActive}:o?{borderLeftColor:d.inputBorderError,borderRightColor:d.inputBorderError,borderTopColor:d.inputBorderError,borderBottomColor:d.inputBorderError,backgroundColor:d.inputFillError}:p?{borderLeftColor:d.inputBorderPositive,borderRightColor:d.inputBorderPositive,borderTopColor:d.inputBorderPositive,borderBottomColor:d.inputBorderPositive,backgroundColor:d.inputFillPositive}:{borderLeftColor:d.inputBorder,borderRightColor:d.inputBorder,borderTopColor:d.inputBorder,borderBottomColor:d.inputBorder,backgroundColor:d.inputFill}}function L(n,e){var o=e.inputBorderRadius;return n===c.SIZE.mini&&(o=e.inputBorderRadiusMini),{borderTopLeftRadius:o,borderBottomLeftRadius:o,borderTopRightRadius:o,borderBottomRightRadius:o}}var B=function(e){var o=e.$isFocused,p=e.$adjoined,d=e.$error,O=e.$disabled,I=e.$positive,R=e.$size,W=e.$theme,T=e.$theme,N=T.borders,U=T.colors,le=T.sizing,ue=T.typography,G=T.animation,se=e.$hasIconTrailing;return v(v(v(v({boxSizing:"border-box",display:"flex",overflow:"hidden",width:"100%",borderLeftWidth:"2px",borderRightWidth:"2px",borderTopWidth:"2px",borderBottomWidth:"2px",borderLeftStyle:"solid",borderRightStyle:"solid",borderTopStyle:"solid",borderBottomStyle:"solid",transitionProperty:"border",transitionDuration:G.timing200,transitionTimingFunction:G.easeOutCurve},L(R,N)),_(R,ue)),D(O,o,d,I,U)),E(p,R,le,W.direction,se))};C.getRootStyles=B;var x=(0,g.styled)("div",B);C.Root=x,x.displayName="Root",x.displayName="Root";function M(n,e){var o;return(o={},y(o,c.SIZE.mini,{paddingRight:e.scale400,paddingLeft:e.scale400}),y(o,c.SIZE.compact,{paddingRight:e.scale400,paddingLeft:e.scale400}),y(o,c.SIZE.default,{paddingRight:e.scale300,paddingLeft:e.scale300}),y(o,c.SIZE.large,{paddingRight:e.scale200,paddingLeft:e.scale200}),o)[n]}function F(n,e,o,p,d){return n?{color:d.inputEnhancerTextDisabled,backgroundColor:d.inputFillDisabled}:e?{color:d.contentPrimary,backgroundColor:d.inputFillActive}:o?{color:d.contentPrimary,backgroundColor:d.inputFillError}:p?{color:d.contentPrimary,backgroundColor:d.inputFillPositive}:{color:d.contentPrimary,backgroundColor:d.inputFill}}var i=(0,g.styled)("div",function(n){var e=n.$size,o=n.$disabled,p=n.$isFocused,d=n.$error,O=n.$positive,I=n.$theme,R=I.colors,W=I.sizing,T=I.typography,N=I.animation;return v(v(v({display:"flex",alignItems:"center",justifyContent:"center",transitionProperty:"color, background-color",transitionDuration:N.timing200,transitionTimingFunction:N.easeOutCurve},_(e,T)),M(e,W)),F(o,p,d,O,R))});C.InputEnhancer=i,i.displayName="InputEnhancer",i.displayName="InputEnhancer";function t(n,e,o,p,d){return n?{color:d.inputTextDisabled,backgroundColor:d.inputFillDisabled}:e?{color:d.contentPrimary,backgroundColor:d.inputFillActive}:o?{color:d.contentPrimary,backgroundColor:d.inputFillError}:p?{color:d.contentPrimary,backgroundColor:d.inputFillPositive}:{color:d.contentPrimary,backgroundColor:d.inputFill}}var u=function(e){var o=e.$isFocused,p=e.$error,d=e.$disabled,O=e.$positive,I=e.$size,R=e.$theme,W=R.colors,T=R.typography,N=R.animation;return v(v({display:"flex",width:"100%",transitionProperty:"background-color",transitionDuration:N.timing200,transitionTimingFunction:N.easeOutCurve},_(I,T)),t(d,o,p,O,W))};C.getInputContainerStyles=u;var s=(0,g.styled)("div",u);C.InputContainer=s,s.displayName="InputContainer",s.displayName="InputContainer";function r(n,e,o,p){return n?{color:p.inputTextDisabled,"-webkit-text-fill-color":p.inputTextDisabled,caretColor:p.contentPrimary,"::placeholder":{color:p.inputPlaceholderDisabled}}:{color:p.contentPrimary,caretColor:p.contentPrimary,"::placeholder":{color:p.inputPlaceholder}}}var a=function(e){var o=e.$disabled,p=e.$isFocused,d=e.$error,O=e.$size,I=e.$theme,R=I.colors,W=I.sizing,T=I.typography;return v(v(v({boxSizing:"border-box",backgroundColor:"transparent",borderLeftWidth:0,borderRightWidth:0,borderTopWidth:0,borderBottomWidth:0,borderLeftStyle:"none",borderRightStyle:"none",borderTopStyle:"none",borderBottomStyle:"none",outline:"none",width:"100%",minWidth:0,maxWidth:"100%",cursor:o?"not-allowed":"text",margin:"0",paddingTop:"0",paddingBottom:"0",paddingLeft:"0",paddingRight:"0"},_(O,T)),P(O,W)),r(o,p,d,R))};C.getInputStyles=a;var l=(0,g.styled)("input",a);return C.Input=l,l.displayName="Input",l.displayName="Input",C}var He=je();const ke=Ie("div",{target:"e1ybf61d0"})({position:"absolute",top:"50%",right:"2.05em"});function Ve({disabled:g,element:c,widgetMgr:h,fragmentId:m}){const[b,v]=Ae({getStateFromWidgetMgr:Ue,getDefaultStateFromProto:Je,getCurrStateFromProto:Ke,updateWidgetMgrState:Ye,element:c,widgetMgr:h,fragmentId:m}),y=V.useContext(Pe),S=J(c.default)&&!g,f=$e(),$={Select:{props:{disabled:g,overrides:{ControlContainer:{style:{height:f.sizes.minElementHeight,borderLeftWidth:f.sizes.borderWidth,borderRightWidth:f.sizes.borderWidth,borderTopWidth:f.sizes.borderWidth,borderBottomWidth:f.sizes.borderWidth}},IconsContainer:{style:()=>({paddingRight:f.spacing.sm})},ValueContainer:{style:()=>({lineHeight:f.lineHeights.inputWidget,paddingRight:f.spacing.sm,paddingLeft:f.spacing.md,paddingBottom:f.spacing.sm,paddingTop:f.spacing.sm})},SingleValue:{style:{fontWeight:f.fontWeights.normal},props:{"data-testid":"stTimeInputTimeDisplay"}},Dropdown:{style:()=>({paddingTop:f.spacing.none,paddingBottom:f.spacing.none,boxShadow:"none",maxHeight:f.sizes.maxDropdownHeight})},DropdownListItem:{style:()=>({paddingRight:f.spacing.lg,paddingLeft:f.spacing.lg,paddingTop:f.spacing.sm,paddingBottom:f.spacing.sm})},Popover:{props:{ignoreBoundary:y,overrides:{Body:{style:()=>({marginTop:f.spacing.px})}}}},Placeholder:{style:()=>({color:f.colors.fadedText60})},SelectArrow:{component:xe,props:{overrides:{Svg:{style:()=>({width:f.iconSizes.xl,height:f.iconSizes.xl})}}}}}}}},P=V.useCallback(_=>{const D=_===null?null:Ge(_);v({value:D,fromUi:!0})},[v]),E=V.useCallback(()=>{P(null)},[P]);return _e("div",{className:"stTimeInput","data-testid":"stTimeInput",children:[z(De,{label:c.label,disabled:g,labelVisibility:Te(c.labelVisibility?.value),children:c.help&&z(Ee,{children:z(Re,{content:c.help,placement:we.TOP_RIGHT})})}),z(We,{format:"24",step:c.step?Number(c.step):900,value:J(b)?void 0:Xe(b),onChange:P,overrides:$,nullable:S,creatable:!0,"aria-label":c.label}),S&&!J(b)&&z(ke,{onClick:E,"data-testid":"stTimeInputClearButton",children:z(He.StyledClearIcon,{overrides:{Svg:{style:{color:f.colors.darkGray,padding:f.spacing.threeXS,height:f.sizes.clearIconSize,width:f.sizes.clearIconSize,":hover":{fill:f.colors.bodyText}}}},$isFocusVisible:!1})})]})}function Ue(g,c){return g.getStringValue(c)??null}function Je(g){return g.default??null}function Ke(g){return g.value??null}function Ye(g,c,h,m){c.setStringValue(g,h.value,{fromUi:h.fromUi},m)}function Ge(g){const c=g.getHours().toString().padStart(2,"0"),h=g.getMinutes().toString().padStart(2,"0");return`${c}:${h}`}function Xe(g){if(g===null)return null;const[c,h]=g.split(":").map(Number),m=new Date;return m.setHours(c),m.setMinutes(h),m}const it=V.memo(Ve);export{it as default};
