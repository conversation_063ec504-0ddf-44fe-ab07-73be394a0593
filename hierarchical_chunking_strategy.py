"""
Hierarchical PDF Chunking Strategy
Purpose: Implement hierarchical chunking approach optimized for RAG retrieval quality
Strategy: Extract complete paragraphs as context, split into sentences for embedding,
         attach full paragraph text as metadata to each sentence chunk
"""

import os
import re
from typing import List, Dict, Any

try:
    import PyPDF2
except ImportError:
    print("Warning: PyPDF2 not installed. Please install with: pip install PyPDF2")
    PyPDF2 = None

try:
    from langchain_text_splitters import RecursiveCharacterTextSplitter
except ImportError:
    print("Warning: langchain_text_splitters not installed. Please install with: pip install langchain-text-splitters")
    RecursiveCharacterTextSplitter = None


def load_pdf(file_path: str) -> str:
    """
    Load PDF content
    
    Args:
        file_path (str): Path to the PDF file
        
    Returns:
        str: Extracted text content from the PDF
        
    Raises:
        ImportError: If PyPDF2 is not installed
        FileNotFoundError: If the PDF file does not exist
        Exception: If PDF reading fails
    """
    if PyPDF2 is None:
        raise ImportError("PyPDF2 is required. Please install with: pip install PyPDF2")
    
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"PDF file not found: {file_path}")
    
    text_content = ""
    
    try:
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            print(f"Reading PDF: {os.path.basename(file_path)}")
            print(f"Total pages: {len(pdf_reader.pages)}")
            
            for page_num, page in enumerate(pdf_reader.pages, 1):
                try:
                    page_text = page.extract_text()
                    if page_text.strip():
                        text_content += page_text + "\n"
                    
                    if page_num % 10 == 0:
                        print(f"Processed {page_num} pages...")
                        
                except Exception as e:
                    print(f"Warning: Failed to read page {page_num}: {str(e)}")
                    continue
            
            print(f"PDF reading complete, total characters: {len(text_content)}")
            
    except Exception as e:
        raise Exception(f"PDF reading failed: {str(e)}")
    
    return text_content


def extract_paragraphs(text: str) -> List[str]:
    """
    Extract complete paragraphs from text
    
    Args:
        text (str): Input text content
        
    Returns:
        List[str]: List of complete paragraph strings
    """
    # Split by double newlines to get paragraphs
    raw_paragraphs = text.split('\n\n')
    
    paragraphs = []
    for para_text in raw_paragraphs:
        # Clean paragraph text
        clean_para = para_text.strip()
        
        # Skip very short paragraphs, page markers, or digit-only content
        if (len(clean_para) < 20 or 
            re.match(r'^--- 第\d+頁 ---$', clean_para) or
            clean_para.isdigit()):
            continue
            
        paragraphs.append(clean_para)
    
    return paragraphs


def split_paragraph_to_sentences(paragraph: str) -> List[str]:
    """
    Split paragraph into individual sentences
    
    Args:
        paragraph (str): Input paragraph text
        
    Returns:
        List[str]: List of individual sentences
    """
    if RecursiveCharacterTextSplitter is None:
        raise ImportError("langchain_text_splitters is required. Please install with: pip install langchain-text-splitters")
    
    # Initialize sentence-level splitter
    sentence_splitter = RecursiveCharacterTextSplitter(
        chunk_size=200,  # Smaller chunk size for sentence-level splitting
        chunk_overlap=20,
        length_function=len,
        separators=[
            ". ",     # English period + space
            "! ",     # English exclamation + space
            "? ",     # English question + space
            "; ",     # English semicolon + space
            "\n",     # Line separator
            " ",      # Space separator
            ""        # Character-level splitting
        ]
    )
    
    # Split paragraph into sentences
    sentences = sentence_splitter.split_text(paragraph)
    
    # Filter out very short sentences
    filtered_sentences = []
    for sentence in sentences:
        clean_sentence = sentence.strip()
        if len(clean_sentence) >= 15:  # Minimum sentence length
            filtered_sentences.append(clean_sentence)
    
    return filtered_sentences


def create_hierarchical_chunks(paragraphs: List[str], source_file: str) -> List[Dict[str, Any]]:
    """
    Create sentence chunks with paragraph context metadata
    
    Args:
        paragraphs (List[str]): List of complete paragraphs
        source_file (str): Source file name
        
    Returns:
        List[Dict[str, Any]]: List of sentence chunks with metadata structure:
            - chunk_text (str): Individual sentence text
            - chunk_index (int): Sequential index of the sentence chunk
            - paragraph_context (str): Full paragraph text containing this sentence
            - paragraph_index (int): Index of the source paragraph
            - source_file (str): Source file name
    """
    hierarchical_chunks = []
    chunk_index = 0
    
    for paragraph_index, paragraph in enumerate(paragraphs):
        # Split paragraph into sentences
        sentences = split_paragraph_to_sentences(paragraph)
        
        # Create chunk for each sentence with paragraph context
        for sentence in sentences:
            chunk_metadata = {
                "chunk_text": sentence,
                "chunk_index": chunk_index,
                "paragraph_context": paragraph,
                "paragraph_index": paragraph_index,
                "source_file": os.path.basename(source_file)
            }
            
            hierarchical_chunks.append(chunk_metadata)
            chunk_index += 1
    
    return hierarchical_chunks


def process_pdf_hierarchical(file_path: str) -> List[Dict[str, Any]]:
    """
    Process PDF file using hierarchical chunking strategy
    
    Args:
        file_path (str): Path to the PDF file
        
    Returns:
        List[Dict[str, Any]]: List of hierarchical chunks with metadata
    """
    print(f"Starting hierarchical chunking for: {os.path.basename(file_path)}")
    
    # Load PDF content
    text_content = load_pdf(file_path)
    
    if not text_content.strip():
        print("Warning: PDF file content is empty")
        return []
    
    # Extract complete paragraphs
    paragraphs = extract_paragraphs(text_content)
    print(f"Extracted {len(paragraphs)} paragraphs")
    
    # Create hierarchical chunks (sentences with paragraph context)
    hierarchical_chunks = create_hierarchical_chunks(paragraphs, file_path)
    
    print(f"Hierarchical chunking complete: {len(paragraphs)} paragraphs → {len(hierarchical_chunks)} sentence chunks")
    
    return hierarchical_chunks


if __name__ == "__main__":
    # Test the hierarchical chunking strategy
    try:
        print("Testing Hierarchical Chunking Strategy")
        print("=" * 45)
        
        # Test with an existing PDF file
        test_file = "./data/The Twelve Houses_ Understanding the Importance of the 12 -- by Howard Sasportas; illustrated by Jacqueline Clare.pdf"
        if os.path.exists(test_file):
            chunks = process_pdf_hierarchical(test_file)
            
            if chunks:
                print(f"\nGenerated {len(chunks)} hierarchical chunks")
                print(f"\nFirst chunk example:")
                first_chunk = chunks[0]
                print(f"Sentence text: {first_chunk['chunk_text']}")
                print(f"Chunk index: {first_chunk['chunk_index']}")
                print(f"Paragraph index: {first_chunk['paragraph_index']}")
                print(f"Source file: {first_chunk['source_file']}")
                print(f"Paragraph context (first 200 chars): {first_chunk['paragraph_context'][:200]}...")
                
                # Show statistics
                unique_paragraphs = set(chunk['paragraph_index'] for chunk in chunks)
                print(f"\nStatistics:")
                print(f"- Total sentence chunks: {len(chunks)}")
                print(f"- Unique paragraphs: {len(unique_paragraphs)}")
                print(f"- Average sentences per paragraph: {len(chunks)/len(unique_paragraphs):.1f}")
        else:
            print(f"Test file not found: {test_file}")
            print("Please place a PDF file in the ./data/ directory for testing")
            
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
